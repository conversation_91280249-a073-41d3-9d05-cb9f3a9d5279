import request from '@/utils/request'

// 查询供应链管理列表
export function listSupply<PERSON>hain(query) {
  return request({
    url: '/tyywpt/tTyywGyl/list',
    method: 'get',
    params: query
  })
}

// 查询供应链管理详细
export function getSupplyChain(id) {
  return request({
    url: '/tyywpt/tTyywGyl/getInfo',
    method: 'get',
    params: { id: id }
  })
}

// 新增供应链管理
export function addSupplyChain(data) {
  return request({
    url: '/tyywpt/tTyywGyl/add',
    method: 'post',
    data: data
  })
}

// 修改供应链管理
export function updateSupplyChain(data) {
  return request({
    url: '/tyywpt/tTyywGyl/edit',
    method: 'put',
    data: data
  })
}

// 删除供应链管理
export function delSupplyChain(ids) {
  return request({
    url: '/tyywpt/tTyywGyl/remove',
    method: 'delete',
    params: { ids: ids }
  })
}

// 导出供应链管理列表
export function exportSupplyChain(query) {
  return request({
    url: '/tyywpt/tTyywGyl/export',
    method: 'post',
    params: query
  })
}

// 获取供应商选项（用于下拉选择）
export function getSuppliers() {
  return request({
    url: '/tyywpt/tTyywGys/list',
    method: 'get',
    params: { pageSize: 1000 } // 获取所有供应商用于下拉选择
  })
}
