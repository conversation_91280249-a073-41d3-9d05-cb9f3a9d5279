<template>
  <div class="wrap flex-c">
    <div class="line" v-for="(item,i) in dataList" :key="i">
      <div class="label">{{item.name}}</div>
      <div class="value">{{ item.value || "-" }}</div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    dataList: {
      type: Array,
      default: () => []
    }
  },
  methods: {

  }
};
</script>

<style lang="scss" scoped>
.wrap {
  flex-wrap: wrap;
}

.line {
  display: flex;
  align-items: center;
  width: 34%;
  margin: 6px 20px 16px 20px;

  .label {
    font-family: Source <PERSON>, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #4e5969;
    line-height: 20px;
    text-align: left;
    width: 120px;
    margin-right: 16px;
    white-space: nowrap;
  }

  .value {
    font-family: Source <PERSON>, Source <PERSON>;
    font-weight: 400;
    font-size: 14px;
    color: #1d2129;
    line-height: 20px;
    text-align: left;
    width: 100%;
  }
}
</style>
