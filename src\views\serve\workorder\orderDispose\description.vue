<template>
  <div class="wrap">
    <div class="line1">
      <div class="item">
        <div class="label">问题描述：</div>
        <div class="value">{{ data.describe || "-" }}</div>
      </div>
    </div>
    <!-- <div class="line1">
      <div class="item">
        <div class="label">影响范围：</div>
        <div class="value">{{ data.yxfw || "-" }}</div>
      </div>
    </div>
    <div class="line1">
      <div class="item">
        <div class="label">处理建议：</div>
        <div class="value">{{ data.cljy || "-" }}</div>
      </div>
    </div> -->
    <div class="line1">
      <div class="item">
        <div class="label">附件：</div>
        <div class="file-display-container">
          <!-- 使用新的 FileDisplay 组件 -->
          <FileDisplay
            v-if="data.fileSuffix && data.fileUrl"
            :fileSuffix="data.fileSuffix"
            :fileUrl="data.fileUrl"
          />

          <!-- 兼容旧的 fj 字段格式 -->
          <div class="fileList flex-c" v-else-if="data.fj">
            <div class="file flex-c" v-for="(item, i) in data.fj" :key="i" @click="previewOldFormatFile(item)">
              <img
                :src="
                  item.type == 'word'
                    ? require('@/assets/images/serve/word.png')
                    : item.type == 'excel'
                    ? require('@/assets/images/serve/excel.png')
                    : item.type == 'pdf'
                    ? require('@/assets/images/serve/pdf.png')
                    : item.type == 'image'
                    ? getImageUrl(item.fileName)
                    : require('@/assets/images/serve/word.png')
                "
                :class="{ 'thumbnail-image': item.type === 'image' }"
              />
              <div class="filename">{{ item.fileName }}</div>
            </div>
          </div>

          <!-- 无附件时的显示 -->
          <div v-else class="no-files">
            暂无附件
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    data: {
      type: Object,
      default: () => {},
    },
  },
  methods: {
    /**
     * 获取图片URL
     * @param {String} fileName - 文件名
     * @returns {String} 完整的图片URL
     */
    getImageUrl(fileName) {
      if (!fileName) return '';

      // 如果已经是完整URL，直接返回
      if (fileName.startsWith('http')) {
        return fileName;
      }

      // 否则拼接基础URL
      const baseUrl = process.env.VUE_APP_BASE_API;
      return `${baseUrl}/${fileName}`;
    },

    /**
     * 预览旧格式的文件
     * @param {Object} file - 文件对象
     */
    previewOldFormatFile(file) {
      const fileUrl = this.getImageUrl(file.fileName);

      if (file.type === 'image') {
        // 图片预览 - 可以使用 Element UI 的图片预览或自定义预览
        this.showImagePreview(fileUrl);
      } else {
        // 其他文件类型 - 新窗口打开
        window.open(fileUrl, '_blank');
      }
    },

    /**
     * 显示图片预览
     * @param {String} imageUrl - 图片URL
     */
    showImagePreview(imageUrl) {
      // 创建一个简单的图片预览
      const img = new Image();
      img.onload = () => {
        const newWindow = window.open('', '_blank');
        newWindow.document.write(`
          <html>
            <head>
              <title>图片预览</title>
              <style>
                body { margin: 0; padding: 20px; background: #f5f5f5; display: flex; justify-content: center; align-items: center; min-height: 100vh; }
                img { max-width: 100%; max-height: 100%; object-fit: contain; box-shadow: 0 4px 12px rgba(0,0,0,0.15); }
              </style>
            </head>
            <body>
              <img src="${imageUrl}" alt="预览图片" />
            </body>
          </html>
        `);
        newWindow.document.close();
      };
      img.onerror = () => {
        this.$message.error('图片加载失败');
      };
      img.src = imageUrl;
    }
  }
};
</script>

<style lang="scss" scoped>
.line1 {
  width: 80%;
  display: flex;
  align-items: center;
  padding: 10px 14px;
  box-sizing: border-box;
}
.item {
  display: flex;
  align-items: center;
  .label {
    width: 90px;
    margin-right: 20px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 16px;
    color: #4e5969;
    line-height: 20px;
    text-align: left;
  }
  .value {
    width: auto;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 16px;
    color: #1d2129;
    line-height: 20px;
    text-align: left;
  }
}
.file-display-container {
  width: 100%;
  margin-top: 10px;
}

.file {
  margin-right: 30px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  img {
    width: 40px;
    height: 40px;
    border-radius: 4px;

    &.thumbnail-image {
      object-fit: cover;
      border: 1px solid #e5e7eb;
    }
  }

  .filename {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #6b7280;
    line-height: 16px;
    text-align: left;
    margin-left: 10px;
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.no-files {
  color: #999;
  font-size: 14px;
  font-style: italic;
}
</style>
