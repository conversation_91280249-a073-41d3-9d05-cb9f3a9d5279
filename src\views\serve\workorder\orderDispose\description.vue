<template>
  <div class="wrap">
    <div class="line1">
      <div class="item">
        <div class="label">问题描述：</div>
        <div class="value">{{ data.describe || "-" }}</div>
      </div>
    </div>
    <!-- <div class="line1">
      <div class="item">
        <div class="label">影响范围：</div>
        <div class="value">{{ data.yxfw || "-" }}</div>
      </div>
    </div>
    <div class="line1">
      <div class="item">
        <div class="label">处理建议：</div>
        <div class="value">{{ data.cljy || "-" }}</div>
      </div>
    </div> -->
    <div class="line1">
      <div class="item">
        <div class="label">附件：</div>
        <div class="fileList flex-c" v-if="data.fj">
          <div class="file flex-c" v-for="(item, i) in data.fj" :key="i">
            <img
              :src="
                item.type == 'word'
                  ? require('@/assets/images/serve/word.png')
                  : item.type == 'pdf'
                  ? require('@/assets/images/serve/pdf.png')
                  : ''
              "
            />
            <div class="filename">{{ item.fileName }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    data: {
      type: Object,
      default: () => {},
    },
  },
};
</script>

<style lang="scss" scoped>
.line1 {
  width: 80%;
  display: flex;
  align-items: center;
  padding: 10px 14px;
  box-sizing: border-box;
}
.item {
  display: flex;
  align-items: center;
  .label {
    width: 90px;
    margin-right: 20px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 16px;
    color: #4e5969;
    line-height: 20px;
    text-align: left;
  }
  .value {
    width: auto;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 16px;
    color: #1d2129;
    line-height: 20px;
    text-align: left;
  }
}
.file {
  margin-right: 30px;
  img {
    width: 40px;
    height: 40px;
  }
  .filename {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #6b7280;
    line-height: 16px;
    text-align: left;
    margin-left: 10px;
  }
}
</style>
