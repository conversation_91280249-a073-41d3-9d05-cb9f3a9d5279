<template>
  <div>
    <el-table :data="datalist">
      <el-table-column :prop="item.key" :label="item.label" align="center" v-for="(item,i) in keyLabelList" :key="i"/>
      <el-table-column label="操作" align="center" width="180" v-if="showControl">
        <template slot-scope="scope">
          <el-button type="text" @click="handleDetail(scope.$index, scope.row)">
            查看
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  props: {
    datalist: {
      type: Array,
      default: () => [],
    },
    keyLabelList: {
      type: Array,
      default: () => []
    },
    showControl: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    handleDetail() {

    }
  }
};
</script>

<style lang="scss" scoped>
</style>
