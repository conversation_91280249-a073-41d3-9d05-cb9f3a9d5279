<template>
  <div>
    <el-table :data="datalist">
      <el-table-column prop="sjlx" label="事件类型" align="center" />
      <el-table-column prop="ssyy" label="所属应用" align="center" />
      <el-table-column prop="ssdw" label="所属单位" align="center" />
      <el-table-column prop="ly" label="来源" align="center" />
      <el-table-column prop="fxdj" label="风险等级" align="center">
        <template slot-scope="scope">
          <div class="flex-c-c">
            <div
              class="tag flex-c-c"
              :class="
                scope.row.fxdj == '严重'
                  ? 'tag_red'
                  : scope.row.fxdj == '中危'
                  ? 'tag_yel'
                  : scope.row.fxdj == '高危'
                  ? 'tag_org'
                  : ''
              "
            >
              {{ scope.row.fxdj }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="jcsj" label="检测时间" align="center" />
      <el-table-column
        prop="ms"
        label="描述"
        align="center"
        :show-overflow-tooltip="true"
      />
      <el-table-column prop="glgd" label="关联工单" align="center" />
      <el-table-column prop="clzt" label="处理状态" align="center">
        <template slot-scope="scope">
          <div class="flex-c-c">
            <div
              class="tag flex-c-c"
              :class="
                scope.row.clzt == '已拦截'
                  ? 'tag_gre'
                  : scope.row.clzt == '处理中'
                  ? 'tag_yel'
                  : 'tag_grey'
              "
            >
              {{ scope.row.clzt }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="goDetail(scope.row)">查看</el-button>
          <el-button
            type="text"
            @click="handleSupervise(scope.row)"
            v-if="scope.row.clzt == '待分析'"
          >
            督办
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  props: {
    datalist: {
      type: Array,
      default: () => [],
    },
  },
  methods: {
    handleDetail(row) {
      this.$emit("goDetail", row);
    },
    handleSupervise(row) {
      this.$emit("supervise", row);
    },
  },
};
</script>

<style lang="scss" scoped>
.tag {
  width: fit-content;
  padding: 4px 12px;
  box-sizing: border-box;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  border-radius: 20px;
}
.tag_blu {
  background: #3ba1ff1a;
  color: #3ba1ff;
}
.tag_red {
  background: #ff00001a;
  color: #ff0000;
}
.tag_yel {
  background: #ffca3a1a;
  color: #ffca3a;
}
.tag_org {
  background: #ff7d001a;
  color: #ff7d00;
}
.tag_gre {
  background: #36cbcb1a;
  color: #36cbcb;
}
.tag_grey {
  background: #1e222a0d;
  color: #1d2129;
}
</style>
