<template>
  <div>
    <el-table :data="datalist">
      <el-table-column prop="yymc" label="应用名称" align="center" />
      <el-table-column prop="yylx" label="应用类型" align="center" />
      <el-table-column prop="hj" label="环境" align="center" />
      <el-table-column prop="kyxzt" label="可用性状态" align="center">
        <template slot-scope="scope">
          <div class="flex-c-c">
            <div
              class="tag flex-c-c"
              :class="
                scope.row.kyxzt == '在线'
                  ? 'tag_blu'
                  : scope.row.kyxzt == '离线'
                  ? 'tag_red'
                  : ''
              "
            >
              {{ scope.row.kyxzt }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="xysj" label="响应时间(ms)" align="center" />
      <el-table-column prop="cwl" label="错误率" align="center" />
      <el-table-column prop="zhjcsj" label="最后检测时间" align="center" />
      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="goDetail(scope.row)">查看</el-button>
          <el-button
            type="text"
            @click="handleDisposal(scope.row)"
            v-if="scope.row.kyxzt == '离线'"
          >
            处置
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  props: {
    datalist: {
      type: Array,
      default: () => [],
    },
  },
  methods: {
    handleDetail(row) {
      this.$emit("goDetail", row);
    },
    handleDisposal(row) {
      this.$emit("dispose", row);
    },
  },
};
</script>

<style lang="scss" scoped>
.tag {
  width: fit-content;
  padding: 4px 12px;
  box-sizing: border-box;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  border-radius: 20px;
}
.tag_blu {
  background: #3ba1ff1a;
  color: #3ba1ff;
}
.tag_red {
  background: #ff00001a;
  color: #ff0000;
}
</style>
