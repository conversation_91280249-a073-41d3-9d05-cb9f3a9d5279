<template>
  <div class="container">
    <!-- 查询条件卡片 -->
    <div class="card">
      <div class="cardTitle">供应链管理</div>
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="组件IP" prop="ip">
          <el-input
            v-model="queryParams.ip"
            placeholder="请输入组件IP"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="类型" prop="type">
          <el-select v-model="queryParams.type" placeholder="请选择类型" clearable>
            <el-option
              v-for="item in typeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="供应商" prop="gys">
          <el-select v-model="queryParams.gys" placeholder="请选择供应商" clearable>
            <el-option
              v-for="item in supplierOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="版本号" prop="bbh">
          <el-input
            v-model="queryParams.bbh"
            placeholder="请输入版本号"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作按钮卡片 -->
    <div class="card">
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
          >新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="el-icon-edit"
            size="mini"
            :disabled="single"
            @click="handleUpdate"
          >修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleDelete"
          >删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
          >导出</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>
    </div>

    <!-- 数据表格卡片 -->
    <div class="card">
      <el-table v-loading="loading" :data="supplyChainList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="组件IP" align="center" prop="ip" />
        <el-table-column label="类型" align="center" prop="type">
          <template slot-scope="scope">
            <span>{{ getTypeName(scope.row.type) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="供应商" align="center" prop="gys">
          <template slot-scope="scope">
            <span>{{ getSupplierName(scope.row.gys) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="版本号" align="center" prop="bbh" />
        <el-table-column label="创建时间" align="center" prop="cTime" width="180">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.cTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
            >修改</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 添加或修改供应链管理对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="组件IP" prop="ip">
          <el-input v-model="form.ip" placeholder="请输入组件IP" />
        </el-form-item>
        <el-form-item label="类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择类型">
            <el-option
              v-for="item in typeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="供应商" prop="gys">
          <el-select v-model="form.gys" placeholder="请选择供应商">
            <el-option
              v-for="item in supplierOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="版本号" prop="bbh">
          <el-input v-model="form.bbh" placeholder="请输入版本号" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listSupplyChain,
  getSupplyChain,
  delSupplyChain,
  addSupplyChain,
  updateSupplyChain,
  getSuppliers
} from "@/api/property/supplyChain";

export default {
  name: "SupplyChainManage",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 供应链管理表格数据
      supplyChainList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        ip: null,
        type: null,
        gys: null,
        bbh: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        ip: [
          { required: true, message: "组件IP不能为空", trigger: "blur" }
        ],
        type: [
          { required: true, message: "类型不能为空", trigger: "change" }
        ],
        gys: [
          { required: true, message: "供应商不能为空", trigger: "change" }
        ],
        bbh: [
          { required: true, message: "版本号不能为空", trigger: "blur" }
        ]
      },
      // 类型选项
      typeOptions: [
        { label: "中间件", value: 1 },
        { label: "系统", value: 2 },
        { label: "数据库", value: 3 }
      ],
      // 供应商选项
      supplierOptions: []
    };
  },
  created() {
    this.getList();
    this.getSupplierOptions();
  },
  methods: {
    /** 查询供应链管理列表 */
    getList() {
      this.loading = true;
      listSupplyChain(this.queryParams).then(response => {
        this.supplyChainList = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    /** 获取供应商选项 */
    getSupplierOptions() {
      getSuppliers().then(response => {
        // 将供应商列表转换为下拉选项格式
        this.supplierOptions = response.data.list.map(item => ({
          label: item.gysName,
          value: item.gysName
        }));
      }).catch(() => {
        // 如果获取失败，使用默认选项
        this.supplierOptions = [
          { label: 'Apache软件基金会', value: 'Apache软件基金会' },
          { label: 'Oracle公司', value: 'Oracle公司' },
          { label: 'Red Hat公司', value: 'Red Hat公司' }
        ];
      });
    },
    /** 获取类型名称 */
    getTypeName(type) {
      const typeItem = this.typeOptions.find(item => item.value === type);
      return typeItem ? typeItem.label : type;
    },
    /** 获取供应商名称 */
    getSupplierName(supplier) {
      const supplierItem = this.supplierOptions.find(item => item.value === supplier);
      return supplierItem ? supplierItem.label : supplier;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        ip: null,
        type: null,
        gys: null,
        bbh: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加供应链管理";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getSupplyChain(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改供应链管理";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateSupplyChain(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addSupplyChain(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除供应链管理编号为"' + ids + '"的数据项？').then(function() {
        return delSupplyChain(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('tyywpt/tTyywGyl/export', {
        ...this.queryParams
      }, `supplyChain_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>

<style lang="scss" scoped>
.container {
  padding-bottom: 40px;
}

.card {
  border-radius: 15px;
  padding: 20px 20px;
  box-sizing: border-box;
  background-color: #fff;
  height: auto;
  margin-bottom: 12px;

  .cardTitle {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 700;
    font-size: 18px;
    color: #1d2129;
    line-height: 24px;
    text-align: left;
    margin-bottom: 10px;
    padding-left: 18px;
    box-sizing: border-box;
    position: relative;
    &::before {
      content: "";
      position: absolute;
      top: 2px;
      left: 0;
      width: 10px;
      height: 20px;
      background: url("~@/assets/images/cardTitle_icon.png");
      background-size: 100% 100%;
    }
  }
}
</style>
