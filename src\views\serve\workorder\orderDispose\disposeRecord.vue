<template>
  <div>
    <div class="list">
      <div class="li" v-for="(item, i) in list" :key="i">
        <div class="clr">{{ item.userName }}</div>
        <div class="flex-b" style="align-items: baseline">
          <div class="content">
            {{ item.content }}
          </div>
          <div class="time">
            {{ item.cTime }}
          </div>
        </div>
        <div class="fileList flex-c" v-if="item.fj">
          <div class="file flex-c" v-for="(x, j) in item.fj" :key="j">
            <img
              :src="
                x.type == 'word'
                  ? require('@/assets/images/serve/word.png')
                  : x.type == 'pdf'
                  ? require('@/assets/images/serve/pdf.png')
                  : ''
              "
            />
            <div class="filename">{{ x.fileName }}</div>
          </div>
        </div>
        <div class="divider" v-if="i !== list.length - 1"></div>
      </div>
    </div>
  </div>
</template>

<script>
import { listGdLog } from "@/api/serve/orderlist";

export default {
  props: {
    gdId: {
      type: String,
      default: "",
    },
    loadFlag: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      list: [],
    };
  },
  mounted() {
    this.init();
  },
  watch: {
    loadFlag() {
      this.init();
    },
  },

  methods: {
    init() {
      console.log('222')
      listGdLog({ gdId: this.gdId }).then((res) => {
        this.list = res.data.list;
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.list {
  padding: 20px;
  box-sizing: border-box;
  .li {
    .name {
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 500;
      font-size: 16px;
      color: #111827;
      line-height: 24px;
      text-align: left;
    }
    .content {
      flex: 10;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 14px;
      color: #1d2129;
      line-height: 24px;
      text-align: left;
      margin-top: 8px;
      margin-right: 80px;
    }
    .time {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 14px;
      color: #4e5969;
      line-height: 20px;
      text-align: left;
    }
    .divider {
      width: 100%;
      height: 1px;
      background-color: #e5e7eb;
      margin: 20px 0;
    }
  }
}
.fileList {
  margin-top: 20px;
  .file {
    margin-right: 30px;
    img {
      width: 40px;
      height: 40px;
    }
    .filename {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 14px;
      color: #6b7280;
      line-height: 16px;
      text-align: left;
      margin-left: 10px;
    }
  }
}
</style>
