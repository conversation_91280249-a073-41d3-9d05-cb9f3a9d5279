<template>
  <div>
    <el-table :data="datalist">
      <el-table-column prop="ym" label="域名" align="center" />
      <el-table-column prop="ip" label="IP" align="center" />
      <el-table-column prop="dk" label="端口" align="center" />
      <el-table-column prop="gxsj" label="更新时间" align="center" />
    </el-table>
  </div>
</template>

<script>
export default {
  props: {
    datalist: {
      type: Array,
      default: () => [],
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
