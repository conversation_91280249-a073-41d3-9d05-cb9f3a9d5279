<template>
  <div class="container">
    <div class="card">
      <el-form
        :model="queryParams"
        ref="queryForm"
        size="small"
        :inline="true"
        label-width="100px"
      >
        <el-form-item label="单位名称" prop="dwmc">
          <el-input
            v-model="queryParams.dwmc"
            placeholder="请输入"
            clearable
            style="width: 180px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="系统名称" prop="xtmc">
          <el-input
            v-model="queryParams.xtmc"
            placeholder="请输入"
            clearable
            style="width: 180px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="展示类型" prop="type">
          <el-select
            v-model="queryParams.type"
            placeholder="请选择"
            clearable
            style="width: 180px"
          >
            <el-option
              v-for="(item, i) in typeOptions"
              :key="i"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="系统域名" prop="xtym">
          <el-input
            v-model="queryParams.xtym"
            placeholder="请输入"
            clearable
            style="width: 180px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">
            查询
          </el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="card flex-b" style="padding: 0">
      <div class="tabList flex-c">
        <div
          class="tab"
          v-for="(item, i) in tabList"
          :key="i"
          :class="tabIndex == i ? 'tab_active' : ''"
          @click="changeTab(i)"
        >
          {{ item }}
        </div>
      </div>
      <div class="total">
        已找到<span class="blue"> {{ total }} </span>个web业务系统
      </div>
    </div>

    <div class="card dataItem" v-for="(item, i) in datalist" :key="i">
      <div class="flex-b">
        <div class="flex-c">
          <div class="name" @click="handleDetail(item)">{{ item.xtmc }}</div>
          <div class="tagList flex-c" v-if="item.tag">
            <div class="tag flex-c-c" v-for="(x, j) in item.tag" :key="j">
              {{ x }}
            </div>
          </div>
        </div>
        <div class="statusTag flex-c-c">{{ item.status }}</div>
      </div>
      <div class="flex-b">
        <div>
          <div class="line flex-c">
            <img src="@/assets/images/monitor/net.png" class="icon" />
            <span class="text"> {{ item.url }}</span>
          </div>
          <div class="line flex-c">
            <img src="@/assets/images/monitor/building.png" class="icon" />
            <span class="text"> {{ item.dwmc }}</span>
          </div>
          <div class="line flex-c">
            <img src="@/assets/images/monitor/link.png" class="icon" />
            <span class="text"> 关联web数：{{ item.glWeb }}</span>
          </div>
        </div>
        <div class="infoCon">
          <div class="infoBox">
            <div class="alignText">
              <img src="@/assets/images/monitor/aqsj.png" class="icon2" />
              <div>安全事件</div>
            </div>
            <div class="text2" :class="item.aqsj ? 'text_red' : ''">
              {{ item.aqsj || "暂无" }}
            </div>
          </div>
          <div class="infoBox">
            <div class="alignText">
              <img src="@/assets/images/monitor/wlgj.png" class="icon2" />
              <div>网络攻击</div>
            </div>
            <div class="text2" :class="item.wlgj ? 'text_red' : ''">
              {{ item.wlgj || "暂无" }}
            </div>
          </div>
          <div class="infoBox">
            <div class="alignText">
              <img src="@/assets/images/monitor/fxyh.png" class="icon2" />
              <div>风险隐患</div>
            </div>
            <div class="text2" :class="item.fxyh ? 'text_red' : ''">
              {{ item.fxyh || "暂无" }}
            </div>
          </div>
        </div>

        <div class="right flex-c">
          <img
            v-if="!item.inCollection"
            src="@/assets/images/monitor/star.png"
            class="icon3"
            @click="handleCollect(item, 1)"
          />
          <i
            v-else
            class="el-icon-star-on"
            style="color: #2563eb; font-size: 22px"
            @click="handleCollect(item, 0)"
          ></i>
          <el-popover
            ref="popover"
            placement="bottom-end"
            trigger="click"
            popper-class="myPopover"
            @show="item.showPopver = true"
            @hide="item.showPopver = false"
          >
            <i
              class="el-icon-more moreBtn"
              slot="reference"
              :style="{ color: item.showPopver ? '#0057FE' : '#9CA3AF' }"
            ></i>
            <div class="popoverList">
              <div
                class="pItem flex-c-c"
                v-for="(x, j) in popoverList"
                :key="j"
                @click="handleApp(item, j)"
              >
                <img :src="x.icon" class="icon" />
                <div class="name">{{ x.name }}</div>
              </div>
            </div>
          </el-popover>
        </div>
      </div>
    </div>
    <div
      class="card"
      style="display: flex; justify-content: flex-end; margin-top: 20px"
    >
      <el-pagination
        @current-change="getList"
        :current-page="queryParams.pageNum"
        :page-size="queryParams.pageNum"
        layout="total, prev, pager, next"
        :total="total"
      ></el-pagination>
    </div>


  </div>
</template>

<script>
export default {
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        dwmc: undefined,
        xtmc: undefined,
        type: undefined,
        xtym: undefined,
      },
      typeOptions: [],
      tabList: ["系统卡片", "系统缩略图", "系统管理"],
      tabIndex: 0,
      total: 100,
      datalist: [
        {
          id: "1",
          xtmc: "市委办整体智治门户",
          url: "dzttzz.swb.jinhua.gov.cn:80",
          dwmc: "金华市委办公室（政研室、档案局）",
          glWeb: "0",
          tag: ["暂无ICP备案", "http", "互联网"],
          aqsj: "11",
          wlgj: "22",
          fxyh: "",
          status: "健康",
          inCollection: false,
          showPopver: false,
        },
        {
          id: "2",
          xtmc: "市委办整体智治门户",
          url: "dzttzz.swb.jinhua.gov.cn:80",
          dwmc: "金华市委办公室（政研室、档案局）",
          glWeb: "",
          tag: ["暂无ICP备案", "http", "互联网"],
          aqsj: "",
          wlgj: "",
          fxyh: "",
          status: "健康",
          inCollection: false,
          showPopver: false,
        },
        {
          id: "3",
          xtmc: "市委办整体智治门户",
          url: "dzttzz.swb.jinhua.gov.cn:80",
          dwmc: "金华市委办公室（政研室、档案局）",
          glWeb: "",
          tag: ["暂无ICP备案", "http", "互联网"],
          aqsj: "",
          wlgj: "",
          fxyh: "",
          status: "健康",
          inCollection: false,
          showPopver: false,
        },
        {
          id: "4",
          xtmc: "市委办整体智治门户",
          url: "dzttzz.swb.jinhua.gov.cn:80",
          dwmc: "金华市委办公室（政研室、档案局）",
          glWeb: "",
          tag: ["暂无ICP备案", "http", "互联网"],
          aqsj: "",
          wlgj: "",
          fxyh: "",
          status: "健康",
          inCollection: false,
          showPopver: false,
        },
      ],
      popoverList: [
        { name: "档案", icon: require("@/assets/images/property/file.png") },
        { name: "编辑", icon: require("@/assets/images/property/edit.png") },
        { name: "删除", icon: require("@/assets/images/property/delete.png") },
      ],
    };
  },
  created() {
    this.getList();
  },
  methods: {
    changeTab(i) {
      this.tabIndex = i;
    },
    getList() {
      console.log("Search keyword:", this.searchKeyword);
    },
    handleCollect(item, type) {
      if (type == 0) {
        item.inCollection = false;
      } else {
        item.inCollection = true;
      }
    },
    handleApp(item, type) {
      if (type == 0) {
        //档案
      } else if (type == 1) {
        //编辑 - 跳转到编辑页面
        this.$router.push({
          path: "applicationManageEdit",
          query: {
            id: item.id,
            formData: item
          }
        });
      } else {
        //删除
        this.$confirm('确定要删除这个应用吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          // 这里应该调用删除API
          console.log("删除应用:", item);
          this.$message.success('删除成功');
        }).catch(() => {});
      }
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        dwmc: undefined,
        xtmc: undefined,
        type: undefined,
        xtym: undefined,
      };
    },
    handleDetail(item) {
      this.$router.push({
        path: "/monitorCenter/applicationDetail",
        query: { id: item.id },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  padding-bottom: 40px;
}
.card {
  border-radius: 15px;
  padding: 20px 20px;
  box-sizing: border-box;
  background-color: #fff;
  height: auto;
  margin-bottom: 12px;
}
.card {
  ::v-deep .el-form-item {
    margin-bottom: 0 !important;
  }
}
.dataItem {
  margin-bottom: 12px !important;
  .name {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 500;
    font-size: 18px;
    color: #1d2129;
    line-height: 24px;
    text-align: left;
    cursor: pointer;
  }
  .tagList {
    margin-left: 12px;
    .tag {
      padding: 4px 10px;
      box-sizing: border-box;
      margin-right: 8px;
      background-color: #eff6ff;
      border-radius: 4px;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 12px;
      color: #2563eb;
      line-height: 16px;
      text-align: left;
    }
  }
  .statusTag {
    border-radius: 20px;
    padding: 4px 12px;
    box-sizing: border-box;
    background-color: #f0fdf4;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #16a34a;
    line-height: 20px;
    text-align: left;
  }
  .line {
    margin-top: 12px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #4e5969;
    line-height: 20px;
    text-align: left;
    .icon {
      width: 14px;
      height: 15px;
      margin-right: 6px;
    }
  }
  .infoCon {
    display: flex;
    justify-content: space-around;
    align-items: center;
    .infoBox {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 14px;
      color: #9ca3af;
      line-height: 20px;
      text-align: center;
      display: flex;
      align-items: flex-start;
      width: 300px;
      margin-left: 20px;
      .alignText {
        text-align: center;
        margin-right: 55px;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 16px;
        color: #4e5969;
        line-height: 24px;
        .icon2 {
          width: 40px;
          height: 40px;
          margin-bottom: 10px;
        }
      }
      .text2 {
        margin-top: 10px;
      }
      .text_red {
        color: orangered;
        font-size: 20px;
      }
    }
  }
  .right {
    width: 50px;
    .icon3 {
      width: 22px;
      height: 28px;
      cursor: pointer;
    }
    .moreBtn {
      cursor: pointer;
      transform: rotate(90deg);
      // color: #9ca3af;
      font-size: 22px;
      margin-left: 12px;
    }
  }
}
.popoverList {
  width: fit-content;

  .pItem {
    width: fit-content;
    border-bottom: solid 1px #d8d8d8;
    padding: 10px 18px;
    box-sizing: border-box;
    cursor: pointer;
    .icon {
      width: 16px;
      height: 16px;
      margin-right: 4px;
    }
    .name {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 14px;
      color: #4e5969;
      line-height: 22px;
      text-align: left;
    }
  }
}
</style>
<style lang="scss">
.el-popover {
  padding: 0;
  min-width: unset;
}
.tabList {
  padding: 0 20px;
  box-sizing: border-box;
  .tab {
    margin-right: 56px;
    padding: 20px 0;
    box-sizing: border-box;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #1d2129;
    line-height: 22px;
    text-align: left;
    cursor: pointer;
  }
  .tab_active {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 700;
    font-size: 14px;
    color: #1d2129;
    line-height: 22px;
    border-bottom: solid 3px #0057fe;
  }
}
.total {
  padding: 20px 30px;
  box-sizing: border-box;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  color: #1d2129;
  line-height: 22px;
  .blue {
    color: #0057fe;
  }
}
</style>
