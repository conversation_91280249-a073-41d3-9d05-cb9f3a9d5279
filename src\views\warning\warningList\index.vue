<template>
  <div class="container">
    <div class="card">
      <div class="cardTitle">应用监控列表</div>
      <yyjk :datalist="yyjkList"></yyjk>
    </div>
    <div class="card">
      <div class="cardTitle">网络资源预警列表</div>
      <wlzy :datalist="wlzyList"></wlzy>
    </div>
    <div class="card">
      <div class="cardTitle">安全隐患列表</div>
      <aqyh :datalist="aqyhList"></aqyh>
    </div>
    <div class="card">
      <div class="cardTitle">安全预警事件列表</div>
      <aqyjsj :datalist="aqyjsjList"></aqyjsj>
    </div>
  </div>
</template>

<script>
import yyjk from "@/views/warning/warningList/components/yyjk.vue";
import wlzy from "@/views/warning/warningList/components/wlzy.vue";
import aqyh from "@/views/warning/warningList/components/aqyh.vue";
import aqyjsj from "@/views/warning/warningList/components/aqyjsj.vue";
export default {
  components: { yyjk, wlzy, aqyh, aqyjsj },
  data() {
    return {
      yyjkList: [
        {
          yymc: "应用名称",
          yylx: "Web应用",
          hj: "生产",
          kyxzt: "在线",
          xysj: "120",
          cwl: "0.3%",
          zhjcsj: "2025-06-05 15:29:59",
        },
        {
          yymc: "应用名称",
          yylx: "Web应用",
          hj: "生产",
          kyxzt: "在线",
          xysj: "120",
          cwl: "0.3%",
          zhjcsj: "2025-06-05 15:29:59",
        },
        {
          yymc: "应用名称",
          yylx: "Web应用",
          hj: "生产",
          kyxzt: "离线",
          xysj: "120",
          cwl: "0.3%",
          zhjcsj: "2025-06-05 15:29:59",
        },
      ],
      wlzyList: [
        {
          bmxx: "IT部",
          fwqlx: "云服务器",
          ip: "*************",
          slid: "i-20231101",
          slmc: "web-server-01",
          slgg: "4核8G",
          lyl: "85%",
          gjdj: "高",
          ssyy: "所属应用",
          ssdw: "所属单位",
          glgd: "ZY20231101-001",
          czzt: "处理中",
        },
        {
          bmxx: "IT部",
          fwqlx: "云服务器",
          ip: "*************",
          slid: "i-20231101",
          slmc: "web-server-01",
          slgg: "4核8G",
          lyl: "85%",
          gjdj: "严重",
          ssyy: "所属应用",
          ssdw: "所属单位",
          glgd: "ZY20231101-001",
          czzt: "待处理",
        },
      ],
      aqyhList: [
        {
          sjlx: "网络攻击事件",
          ssyy: "所属应用",
          ssdw: "所属单位",
          ly: "WAF防护",
          fxdj: "高危",
          jcsj: "2025-06-05 15:29:59",
          ms: "描述描述描述描述描述描述描述",
          glgd: "AQ20231101-001",
          clzt: "已拦截",
        },
        {
          sjlx: "网络攻击事件",
          ssyy: "所属应用",
          ssdw: "所属单位",
          ly: "WAF防护",
          fxdj: "中危",
          jcsj: "2025-06-05 15:29:59",
          ms: "描述描述描述描述描述描述描述",
          glgd: "AQ20231101-001",
          clzt: "待分析",
        },
        {
          sjlx: "网络攻击事件",
          ssyy: "所属应用",
          ssdw: "所属单位",
          ly: "WAF防护",
          fxdj: "严重",
          jcsj: "2025-06-05 15:29:59",
          ms: "描述描述描述描述描述描述描述",
          glgd: "AQ20231101-001",
          clzt: "处理中",
        },
      ],
      aqyjsjList: [
        {
          yhlx: "弱口令",
          fxdj: "高危",
          zcxx: "*************:22",
          ssyy: "所属应用",
          ssdw: "所属单位",
          jcsj: "2025-06-05 15:29:59",
          ms: "用户admin使用弱口令，强度评分：15/100",
          fxpf: "95",
          clzt: "待处理",
        },
        {
          yhlx: "弱口令",
          fxdj: "严重",
          zcxx: "*************:22",
          ssyy: "所属应用",
          ssdw: "所属单位",
          jcsj: "2025-06-05 15:29:59",
          ms: "用户admin使用弱口令，强度评分：15/100",
          fxpf: "98",
          clzt: "处理中",
        },
        {
          yhlx: "弱口令",
          fxdj: "高危",
          zcxx: "*************:22",
          ssyy: "所属应用",
          ssdw: "所属单位",
          jcsj: "2025-06-05 15:29:59",
          ms: "用户admin使用弱口令，强度评分：15/100",
          fxpf: "88",
          clzt: "待处理",
        },
        {
          yhlx: "弱口令",
          fxdj: "中危",
          zcxx: "*************:22",
          ssyy: "所属应用",
          ssdw: "所属单位",
          jcsj: "2025-06-05 15:29:59",
          ms: "用户admin使用弱口令，强度评分：15/100",
          fxpf: "75",
          clzt: "已修复",
        },
      ],
    };
  },
  methods: {},
};
</script>

<style lang="scss" scoped>
.container {
  padding-bottom: 40px;
}
.card {
  border-radius: 15px;
  padding: 20px 20px;
  box-sizing: border-box;
  background-color: #fff;
  height: auto;
  margin-bottom: 12px;
  .cardTitle {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 700;
    font-size: 18px;
    color: #1d2129;
    line-height: 24px;
    text-align: left;
    margin-right: 30px;
    margin-bottom: 10px;
  }
}
::v-deep .el-table__cell {
  padding: 6px 0;
}
</style>
