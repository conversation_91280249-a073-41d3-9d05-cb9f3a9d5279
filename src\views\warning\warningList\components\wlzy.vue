<template>
  <div>
    <el-table :data="datalist">
      <el-table-column prop="bmxx" label="部门信息" align="center" />
      <el-table-column prop="fwqlx" label="服务器类型" align="center" />
      <el-table-column prop="ip" label="IP" align="center" />
      <el-table-column prop="slid" label="实例ID" align="center" />
      <el-table-column prop="slmc" label="实例名称" align="center" />
      <el-table-column prop="slgg" label="实例规格" align="center" />
      <el-table-column prop="lyl" label="利用率" align="center" />
      <el-table-column prop="gjdj" label="告警等级" align="center">
        <template slot-scope="scope">
          <div class="flex-c-c">
            <div
              class="tag flex-c-c"
              :class="
                scope.row.gjdj == '严重'
                  ? 'tag_red'
                  : scope.row.gjdj == '高' || scope.row.gjdj == '待处理'
                  ? 'tag_org'
                  : scope.row.gjdj == '处理中'
                  ? 'tag_yel'
                  : ''
              "
            >
              {{ scope.row.gjdj }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="ssyy" label="所属应用" align="center" />
      <el-table-column prop="ssdw" label="所属单位" align="center" />
      <el-table-column prop="glgd" label="关联工单" align="center" />
      <el-table-column prop="czzt" label="告警等级" align="center">
        <template slot-scope="scope">
          <div class="flex-c-c">
            <div
              class="tag flex-c-c"
              :class="
                scope.row.czzt == '严重'
                  ? 'tag_red'
                  : scope.row.czzt == '高' || scope.row.czzt == '待处理'
                  ? 'tag_org'
                  : scope.row.czzt == '处理中'
                  ? 'tag_yel'
                  : ''
              "
            >
              {{ scope.row.czzt }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="goDetail(scope.row)">查看</el-button>
          <el-button
            type="text"
            @click="handleSupervise(scope.row)"
            v-if="scope.row.czzt == '待处理'"
          >
            督办
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  props: {
    datalist: {
      type: Array,
      default: () => [],
    },
  },
  methods: {
    handleDetail(row) {
      this.$emit("goDetail", row);
    },
    handleSupervise(row) {
      this.$emit("supervise", row);
    },
  },
};
</script>

<style lang="scss" scoped>
.tag {
  width: fit-content;
  padding: 4px 12px;
  box-sizing: border-box;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  border-radius: 20px;
}
.tag_blu {
  background: #3ba1ff1a;
  color: #3ba1ff;
}
.tag_red {
  background: #ff00001a;
  color: #ff0000;
}
.tag_yel {
  background: #ffca3a1a;
  color: #ffca3a;
}
.tag_org {
  background: #ff7d001a;
  color: #ff7d00;
}
</style>
