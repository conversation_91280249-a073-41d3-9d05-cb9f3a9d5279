<template>
  <div class="wrap">
    <div class="line3">
      <div class="item">
        <div class="label">工单类型</div>
        <div class="value">{{ formatGdType(data.gdType) }}</div>
      </div>
      <div class="item">
        <div class="label">创建时间</div>
        <div class="value">{{ data.cTime || "-" }}</div>
      </div>
      <div class="item">
        <div class="label">创建人</div>
        <div class="value">{{ data.createName || "-" }}</div>
      </div>
    </div>
    <div class="line3">
      <div class="item">
        <div class="label">处理人</div>
        <div class="value">{{ data.handlerName || "-" }}</div>
      </div>
      <div class="item">
        <div class="label">优先级</div>
        <div
          class="tag flex-c-c"
          v-if="data.priority"
          :class="
            data.priority == 3
              ? 'tag_red'
              : data.priority == 2
              ? 'tag_org'
              : 'tag_blue'
          "
        >
          {{ formatPriority(data.priority) }}
        </div>
        <div v-else>-</div>
      </div>
      <div class="item">
        <div class="label">所属系统</div>
        <div class="value">{{ data.yyName || "-" }}</div>
      </div>
    </div>
    <!-- <div class="line3">
      <div class="item">
        <div class="label">关联资源</div>
        <div class="value">{{ data.glzy || "-" }}</div>
      </div>
      <div class="item">
        <div class="label">关联告警</div>
        <div class="value">{{ data.glgj || "-" }}</div>
      </div>
      <div class="item">
        <div class="label">SLA</div>
        <div class="value">{{ data.sla || "-" }}</div>
      </div>
    </div> -->
  </div>
</template>

<script>
export default {
  props: {
    data: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      gdTypeOptions: [
        { label: "云资源预警工单", value: 1 },
        { label: "安全预警工单", value: 2 },
        { label: "数据库预警（美创）", value: 3 },
        { label: "bug修复", value: 4 },
        { label: "性能优化", value: 5 },
        { label: "取数", value: 6 },
        { label: "配置修改", value: 7 },
        { label: "其他", value: 8 },
      ],
      priorityOptions: [
        { label: "低", value: 1 },
        { label: "中", value: 2 },
        { label: "高", value: 3 },
      ],
    };
  },
  methods: {
    formatGdType(obj) {
      if (this.gdTypeOptions.find((item) => item.value == obj)) {
        return this.gdTypeOptions.find((item) => item.value == obj).label;
      }
    },
    formatPriority(obj) {
      return this.priorityOptions.find((item) => item.value == obj).label;
    },
  },
};
</script>

<style lang="scss" scoped>
.line3 {
  width: 80%;
  display: flex;
  align-items: center;
  padding: 10px 14px;
  box-sizing: border-box;
}
.item {
  flex: 1;
  display: flex;
  align-items: center;
  margin-right: 20px;
  .label {
    width: 80px;
    margin-right: 20px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 16px;
    color: #4e5969;
    line-height: 20px;
    text-align: left;
  }
  .value {
    width: auto;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 16px;
    color: #1d2129;
    line-height: 20px;
    text-align: left;
  }
  .tag {
    width: 28px;
    height: 20px;
    border-radius: 6px 6px 6px 6px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #ffffff;
  }
  .tag_red {
    background-color: #ff324b;
  }
  .tag_org {
    background-color: #f98f1c;
  }
  .tag_blue {
    background-color: #36d1ff;
  }
}
</style>
