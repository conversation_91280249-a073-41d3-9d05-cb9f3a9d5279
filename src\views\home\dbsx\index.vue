<template>
  <div class="wrap">
    <div class="list1" v-if="tabIndex == 0">
      <div class="li flex-b" v-for="(item, i) in list1" :key="i">
        <div class="flex-c">
          <img :src="item.icon" class="icon" />
          <div class="name">{{ item.name }}</div>
        </div>
        <div class="num">{{ item.num }}<span class="unit">个</span></div>
      </div>
    </div>
    <div class="list2" v-else>
      <div
        class="li flex-c"
        v-for="(item, i) in list2"
        :key="i"
        style="align-items: flex-start"
      >
        <div
          class="tag flex-c-c"
          :class="
            item.tag == '紧急' ? 'tag_red' : item.tag == '重要' ? 'tag_org' : ''
          "
        >
          {{ item.tag }}
        </div>
        <div class="name">{{ item.name }}</div>
        <div class="time">{{ item.time }}</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    tabIndex: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      list1: [
        {
          name: "安全告警事件",
          num: 3,
          icon: require("@/assets/images/ywmh/aqgj.png"),
        },
        {
          name: "云资源告警事件",
          num: 2,
          icon: require("@/assets/images/ywmh/yzygj.png"),
        },
        {
          name: "隐患告警事件",
          num: 1,
          icon: require("@/assets/images/ywmh/yhgj.png"),
        },
      ],
      list2: [
        {
          name: "核心交换机维护通知核心交换机维护通知核心交换机维护通知核心交换机维护通知",
          tag: "紧急",
          time: "2025-06-06",
        },
        { name: "新版本上线公告", tag: "重要", time: "2025-06-06" },
        { name: "季度运维报告发布", tag: "常规", time: "2025-06-06" },
        {
          name: "核心交换机维护通知核心交换机维护通知核心交换机维护通知核心交换机维护通知",
          tag: "紧急",
          time: "2025-06-06",
        },
        { name: "新版本上线公告", tag: "重要", time: "2025-06-06" },
      ],
    };
  },
};
</script>

<style lang="scss" scoped>
.wrap {
  height: inherit;
}
.list1 {
  width: 100%;
  height: 260px;
  overflow-y: scroll;
  &::-webkit-scrollbar {
    display: none;
  }
  .li {
    margin-top: 15px;
    width: 100%;
    height: 60px;
    padding: 10px 20px 10px 13px;
    box-sizing: border-box;
    border-radius: 10px 10px 10px 10px;
    border: 1px solid #e8ebf1;
    .icon {
      width: 40px;
      height: 40px;
    }
    .name {
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 400;
      font-size: 16px;
      color: #4e5969;
      line-height: 28px;
      text-align: left;
      margin-left: 10px;
    }
    .num {
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 400;
      font-size: 24px;
      color: #0057fe;
      line-height: 42px;
      .unit {
        font-size: 18px;
      }
    }
  }
}
.list2 {
  width: 100%;
  height: 260px;
  overflow-y: scroll;
  &::-webkit-scrollbar {
    display: none;
  }
  .li {
    margin-top: 20px;
    .name {
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 400;
      font-size: 16px;
      color: #4e5969;
      line-height: 24px;
      text-align: left;
      width: 80%;
      // overflow: hidden;
      // text-overflow: ellipsis;
      // white-space: nowrap;
    }
    .tag {
      padding: 1px 10px;
      box-sizing: border-box;
      background-color: #e8f3ff;
      font-size: 16px;
      color: #165dff;
      line-height: 26px;
      border-radius: 3px;
      margin-right: 12px;
      white-space: nowrap;
    }
    .tag_red {
      background-color: #ffecec;
      color: #ff0000;
    }
    .tag_org {
      background-color: #fff7e8;
      color: #ff7d00;
    }
    .time {
      white-space: nowrap;
      color: #8993a1;
      margin-top: 4px;
      margin-left: 10px;
    }
  }
}
</style>
