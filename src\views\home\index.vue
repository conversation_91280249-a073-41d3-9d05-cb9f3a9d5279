<template>
  <div class="container">
    <div class="flex-stretch">
      <div class="card" style="width: 75%; margin-right: 12px">
        <div class="cardTitle">运维概览</div>
        <ywgl></ywgl>
      </div>
      <div class="card" style="width: 24%">
        <div class="flex-c">
          <div
            class="titleTab2"
            v-for="(x, i) in titleTabList"
            :key="i"
            :class="tabIndex == i ? 'titleTab' : ''"
            @click="changeTab(i)"
          >
            {{ x }}
          </div>
        </div>
        <dbsx :tabIndex="tabIndex"></dbsx>
      </div>
    </div>
    <div class="card">
      <div class="flex-b">
        <div class="cardTitle">应用清单</div>
        <div class="flex-c">
          <el-input
            v-model="queryParams.name"
            placeholder="应用名称"
            clearable
            style="width: 150px"
            suffix-icon="el-icon-search"
            @keyup.enter.native="getList"
            size="small"
          ></el-input>
          <el-select
            v-model="queryParams.dept"
            placeholder="所属部门"
            clearable
            size="small"
            style="width: 150px; margin-left: 20px"
            @change="getList"
          >
            <el-option
              v-for="dict in deptOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </div>
      </div>
      <yyqd :datalist="datalist" style="margin-top: 20px"></yyqd>
    </div>
    <div class="flex-stretch">
      <div class="card" style="width: 33%; margin-right: 12px">
        <div class="cardTitle">运维子系统</div>
        <div class="itemList">
          <div class="item" v-for="(item, i) in zxtList" :key="i">
            <img :src="item.icon" class="icon" />
            <div class="name">{{ item.name }}</div>
          </div>
        </div>
      </div>
      <div class="card" style="width: 33%; margin-right: 12px">
        <div class="cardTitle">运维工具</div>
        <div class="itemList">
          <div class="item" v-for="(item, i) in ywgjList" :key="i">
            <img :src="item.icon" class="icon" />
            <div class="name">{{ item.name }}</div>
          </div>
        </div>
      </div>
      <div class="card" style="width: 33%">
        <div class="cardTitle">
          快捷方式
          <span style="cursor: poniter" @click="showEdit = !showEdit">
            <i class="el-icon-edit"></i>
          </span>
        </div>
        <div class="itemList">
          <div class="item" v-for="(item, i) in kjfsList" :key="i">
            <img :src="item.icon" class="icon" />
            <img
              src="@/assets/images/ywmh/close_red.png"
              class="btn"
              v-if="showEdit"
              @click="editShortcuts(i, item)"
            />
            <div class="name">{{ item.name }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ywgl from "./ywgl/index.vue";
import dbsx from "./dbsx/index.vue";
import yyqd from "./yyqd/index.vue";
export default {
  components: { ywgl, dbsx, yyqd },
  data() {
    return {
      tabIndex: 0,
      titleTabList: ["待办事项", "通知公告"],
      queryParams: {
        name: "",
      },
      deptOptions: [{ label: "全部", value: "全部" }],
      datalist: [],
      //运维子系统
      zxtList: [
        {
          name: "云管平台",
          url: "",
          icon: require("@/assets/images/ywmh/ygpt.png"),
        },
        {
          name: "风暴中心",
          url: "",
          icon: require("@/assets/images/ywmh/fbzx.png"),
        },
        {
          name: "数据安全在线平台",
          url: "",
          icon: require("@/assets/images/ywmh/sjaqzxpt.png"),
        },
        {
          name: "端点检测平台",
          url: "",
          icon: require("@/assets/images/ywmh/ddjcpt.png"),
        },
      ],
      //运维工具
      ywgjList: [
        {
          name: "批量脚本执行",
          url: "",
          icon: require("@/assets/images/ywmh/pljbzx.png"),
        },
        {
          name: "配置检查工具",
          url: "",
          icon: require("@/assets/images/ywmh/pzjcgj.png"),
        },
        {
          name: "自动化部署",
          url: "",
          icon: require("@/assets/images/ywmh/zdhbs.png"),
        },
        {
          name: "故障诊断工具",
          url: "",
          icon: require("@/assets/images/ywmh/gzzdgj.png"),
        },
      ],
      //快捷方式
      kjfsList: [
        {
          name: "工单提交",
          url: "",
          icon: require("@/assets/images/ywmh/gdtj.png"),
        },
        {
          name: "监控大屏",
          url: "",
          icon: require("@/assets/images/ywmh/jkdp.png"),
        },
        {
          name: "资产查询",
          url: "",
          icon: require("@/assets/images/ywmh/zccx.png"),
        },
        {
          name: "备份管理",
          url: "",
          icon: require("@/assets/images/ywmh/bfgl.png"),
        },
      ],
      showEdit: false,
    };
  },

  methods: {
    changeTab(i) {
      this.tabIndex = i;
    },
    getList() {
      console.log("getlist");
    },
    editShortcuts(i) {
      this.$confirm("确定要删除该快捷方式吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          console.log(i);
          this.kjfsList.splice(i, 1);
        })
        .catch(() => {});
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  // padding: 0px 20px;
  padding-bottom: 40px;
  box-sizing: border-box;
}
.card {
  border-radius: 15px;
  padding: 20px 20px;
  box-sizing: border-box;
  background-color: #fff;
  height: auto;
  margin-bottom: 12px;
  .cardTitle {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 700;
    font-size: 18px;
    color: #1d2129;
    line-height: 24px;
    text-align: left;
    margin-right: 30px;
    margin-bottom: 10px;
  }
  .titleTab2 {
    color: #667085;
    font-weight: 500;
    font-size: 18px;
    line-height: 24px;
    text-align: left;
    margin-right: 30px;
    cursor: pointer;
  }
  .titleTab {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 700;
    font-size: 18px;
    color: #1d2129;
    line-height: 24px;
    text-align: left;
    margin-right: 30px;
    cursor: pointer;
  }
}
.itemList {
  display: flex;
  justify-content: space-around;
  margin-top: 20px;
  .item {
    text-align: center;
    cursor: pointer;
    position: relative;
  }
  .icon {
    width: 40px;
    height: 40px;
    margin-bottom: 10px;
  }
  .name {
    max-width: 6em;
    font-size: 14px;
    color: #4e5969;
    line-height: 20px;
    text-align: center;
  }
  .btn {
    position: absolute;
    right: 2px;
    top: -4px;
    width: 16px;
    height: 16px;
    z-index: 2;
  }
}
.flex-stretch {
  display: flex;
  align-items: stretch;
}
</style>
