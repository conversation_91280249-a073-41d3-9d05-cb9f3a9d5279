<template>
  <div>
    <el-table :data="datalist">
      <el-table-column prop="lx" label="类型" align="center" />
      <el-table-column prop="gg" label="规格" align="center" />
      <el-table-column prop="cpusyl" label="CPU使用率" align="center" />
      <el-table-column prop="ncsyl" label="内存使用率" align="center" />
      <el-table-column prop="ccsyl" label="存储使用率" align="center" />
      <el-table-column prop="zt" label="状态" align="center" />
      <el-table-column prop="ktsj" label="开通时间" align="center" />
      <el-table-column prop="jzsj" label="截止时间" align="center" />
      <el-table-column label="操作" align="center" width="180">
        <template slot-scope="scope">
          <el-button type="text" @click="handleDetail(scope.$index, scope.row)">
            查看
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  props: {
    datalist: {
      type: Array,
      default: () => [],
    },
  },
  methods: {
    handleDetail(i, row) {
      this.$emit("goDetail", row);
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
