<template>
  <div class="wrap flex-b">
    <div class="aqItem flex-b" v-for="(item, i) in list" :key="i">
      <img :src="item.icon" class="icon" />
      <div class="info">
        <div class="name">{{ item.name }}</div>
        <div class="flex-b">
          <div class="num" v-if="item.name != '断点监测'">
            最近7天：
            <span class="blue">
              {{ item.num == 0 ? 0 : item.num ? item.num : "-" }}
            </span>
            个
          </div>
          <div v-else>
            {{ item.status?'正常':'异常' }}
          </div>
          <div class="history" @click="handleViewClick(item)">查看</div>
        </div>
      </div>
    </div>

    <!-- 断点监测弹窗 -->
    <el-dialog
      title="断点监测"
      :visible.sync="breakpointDialogVisible"
      width="800px"
      :before-close="handleCloseBreakpointDialog"
    >
      <el-table :data="breakpointMonitorData" border>
        <el-table-column prop="monitorIp" label="监测IP" align="center" />
        <el-table-column prop="monitorTime" label="监测时间" align="center" />
        <el-table-column prop="ipStatus" label="IP状态" align="center">
          <template slot-scope="scope">
            <el-tag :type="scope.row.ipStatus === '正常' ? 'success' : 'danger'">
              {{ scope.row.ipStatus }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>

      <div slot="footer" class="dialog-footer">
        <el-button @click="breakpointDialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      list: [
        {
          name: "待办工单",
          num: 0,
          icon: require("@/assets/images/monitor/dbgd.png"),
          path: "/serve/workorder"
        },
        {
          name: "通报告警",
          num: 0,
          icon: require("@/assets/images/monitor/tgbj.png"),
          path: "/warning/warningList"
        },
        {
          name: "断点监测",
          status: true,
          icon: require("@/assets/images/monitor/ddjc.png"),
          path: ""
        },
      ],
      // 断点监测弹窗相关数据
      breakpointDialogVisible: false,
      breakpointMonitorData: []
    }
  },
  methods: {
    // 处理查看按钮点击
    handleViewClick(item) {
      if (item.name === '断点监测') {
        this.showBreakpointDialog();
      } else if (item.path) {
        this.jump(item.path);
      }
    },

    // 跳转到其他页面
    jump(path) {
      this.$router.push({
        path: path
      });
    },

    // 显示断点监测弹窗
    showBreakpointDialog() {
      this.loadBreakpointMonitorData();
      this.breakpointDialogVisible = true;
    },

    // 加载断点监测数据
    loadBreakpointMonitorData() {
      // 模拟断点监测数据，实际应该调用API获取
      this.breakpointMonitorData = [
        {
          monitorIp: "*************",
          monitorTime: "2025-01-11 10:30:00",
          ipStatus: "正常"
        },
        {
          monitorIp: "*************",
          monitorTime: "2025-01-11 10:25:00",
          ipStatus: "正常"
        },
        {
          monitorIp: "*************",
          monitorTime: "2025-01-11 10:20:00",
          ipStatus: "异常"
        },
        {
          monitorIp: "*************",
          monitorTime: "2025-01-11 10:15:00",
          ipStatus: "正常"
        },
        {
          monitorIp: "*************",
          monitorTime: "2025-01-11 10:10:00",
          ipStatus: "正常"
        }
      ];
    },

    // 关闭断点监测弹窗
    handleCloseBreakpointDialog() {
      this.breakpointDialogVisible = false;
      this.breakpointMonitorData = [];
    }
  }
};
</script>

<style lang="scss" scoped>
.aqItem {
  padding: 12px;
  box-sizing: border-box;
  flex: 1;
  margin-right: 40px;
  .icon {
    width: 82px;
    height: 82px;
    margin-right: 16px;
  }
  .info {
    width: 100%;
    height: 72px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .name {
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 500;
      font-size: 18px;
      color: #4e5969;
      line-height: 24px;
      text-align: left;
    }
    .num {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 16px;
      color: #4e5969;
      line-height: 24px;
      text-align: left;
      .blue {
        color: #0057fe;
      }
    }
    .history {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 16px;
      color: #667085;
      line-height: 24px;
      text-align: left;
      cursor: pointer;
    }
  }
}
</style>
