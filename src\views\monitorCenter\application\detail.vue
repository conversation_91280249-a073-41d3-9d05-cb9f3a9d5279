<template>
  <div class="container">
    <div class="tabCon">
      <Tab2
        :tablist="tablist"
        :tabIndex="tabIndex"
        @changeTab="changeTab"
        :systemName="data.systemName"
        :tag="data.tag"
      ></Tab2>
    </div>
    <div class="tabCon-placeholder" v-if="isFixed"></div>
    <div class="card" id="baseInfo">
      <div class="cardTitle">基本信息</div>
      <commonInfo :dataList="jcxx"></commonInfo>
    </div>
    <div class="card" id="deptInfo">
      <div class="cardTitle">单位信息</div>
      <commonInfo :dataList="dwxx"></commonInfo>
    </div>
    <div class="card" id="ManuInfo">
      <div class="cardTitle">厂商信息</div>
      <commonInfo :dataList="csxx"></commonInfo>
    </div>
    <div class="card" id="SecurityInfo">
      <div class="cardTitle">安全信息</div>
      <commonInfo :dataList="aqxx"></commonInfo>
    </div>
    <div class="card" id="health">
      <div class="cardTitle">健康监测</div>
      <health />
    </div>
    <div class="card" id="cloudResource">
      <div class="cardTitle">云资源</div>
      <commonTable :datalist="yzy" :keyLabelList="yzyKLlist"></commonTable>
    </div>
    <div class="card" id="systemIp">
      <div class="cardTitle">系统地址</div>
      <commonTable :datalist="xtdz" :keyLabelList="xtdzlist"></commonTable>
    </div>
    <div class="card" id="relatedProgram">
      <div class="cardTitle">关联项目</div>
      <commonTable :datalist="glxm" :keyLabelList="glxmlist"></commonTable>
    </div>
    <div class="card" id="relatedIp">
      <div class="cardTitle">关联IP</div>
      <commonTable :datalist="glip" :keyLabelList="gliplist"></commonTable>
    </div>









<!--    <div class="card" id="security">-->
<!--      <div class="cardTitle">安全监测</div>-->
<!--      <security :aqlist="data.securityList"></security>-->
<!--    </div>-->
<!--    <div class="card" id="cloudResource">-->
<!--      <div class="cardTitle">云资源</div>-->
<!--      <cloudResource :datalist="data.cloudResourceList"></cloudResource>-->
<!--    </div>-->
<!--    <div class="card" id="contacts">-->
<!--      <div class="cardTitle">联系人</div>-->
<!--      <contacts :data="data.contactsData"></contacts>-->
<!--    </div>-->
<!--    <div class="card" id="allFields">-->
<!--      <div class="cardTitle">全部字段</div>-->
<!--      <allFields :list="data.fieldsList"></allFields>-->
<!--    </div>-->
  </div>
</template>

<script>
import Tab2 from "@/views/monitorCenter/application/components/Tab2.vue";
import commonInfo from "@/views/monitorCenter/application/components/commonInfo.vue";
import commonTable from "@/views/monitorCenter/application/components/commonTable.vue";
import baseInfo from "@/views/monitorCenter/application/baseInfo.vue";
import systemIp from "@/views/monitorCenter/application/systemIp.vue";
import relatedProgram from "@/views/monitorCenter/application/relatedProgram.vue";
import cloudResource from "@/views/monitorCenter/application/cloudResource.vue";
import relatedIp from "@/views/monitorCenter/application/relatedIp.vue";
import security from "@/views/monitorCenter/application/security.vue";
import contacts from "@/views/monitorCenter/application/contacts.vue";
import deptInfo from "@/views/monitorCenter/application/deptInfo.vue";
import allFields from "@/views/monitorCenter/application/allFields.vue";
import health from "@/views/monitorCenter/application/health";

export default {
  components: {
    Tab2,
    commonInfo,
    commonTable,
    baseInfo,
    systemIp,
    relatedProgram,
    cloudResource,
    relatedIp,
    security,
    contacts,
    deptInfo,
    allFields,
    health
  },
  data() {
    return {
      isFixed: false,
      tabIndex: 0,
      tablist: [
        { name: "基本信息", href: "baseInfo" },
        { name: "单位信息", href: "deptInfo" },
        { name: "厂商信息", href: "ManuInfo" },
        { name: "安全信息", href: "SecurityInfo" },
        { name: "健康监测", href: "health" },
        { name: "云资源", href: "cloudResource" },
        { name: "系统地址", href: "systemIp" },
        { name: "关联项目", href: "relatedProgram" },
        { name: "关联IP", href: "relatedIp" },
        // { name: "安全监测", href: "security" },
        // { name: "联系人", href: "contacts" },
        // { name: "全部字段", href: "allFields" },
      ],
      data: {
        systemName: "金华市基层治理系统",
        tag: ["互联网"],
        dwmc: "金华市委办公室（政研室、档案局）",
        sxsj: "2025-03-11",
        ywjzsj: "2027-12-31",
        sysj: "2025-06-16",
        jfxx: "1",
        ywry: "1",
        xtbm: "1",
        xtzyx: "1",
        sfhlwxt: "是",
        xtzt: "运行中",

        yybm: "A330701374210202203016675",
        yygly: "程诗悦",
        yylx: "业务类",
        jsyj: "金委办发〔2020〕33 号",
        jscj: "市级",
        tjfw: "市域统建",
        yhfw: "本级政府用户,地方各级政府用户",
        fbd: "浙政钉,网页",
        wlhj: "政务外网",
        zwfw: "未涉及政务服务事项业务（含公共服务事项）",
        ssxt: "党政机关整体智治,数字政府,数字经济,数字社会",
        lzly: "社会治理",
        ejly: "城市运行管理",
        yyly: "医疗卫生,社保就业,公共安全,城建住房,交通运输,教育文化,科技创新,生态环境,工业农业,商贸流通,安全生产,市场监管,生活服务,气象服务,地理空间",
        sfxt: "是",
        dkcj: "是",
        txgt: "是",

        systemIpList: [
          {
            ym: "***********",
            ip: "***********",
            dk: "***********",
            gxsj: "2025-06-10",
          },
          {
            ym: "***********",
            ip: "***********",
            dk: "***********",
            gxsj: "2025-06-10",
          },
        ],
        relatedProgramsList: [
          {
            xmbm: "12345678",
            xmmc: "项目名称",
            ssbm: "部门名称",
            lxsj: "2023-11-10",
          },
          {
            xmbm: "12345678",
            xmmc: "项目名称",
            ssbm: "部门名称",
            lxsj: "2023-11-10",
          },
        ],
        cloudResourceList: [
          {
            lx: "ECS",
            gg: "8G+512G",
            cpusyl: "33%",
            ncsyl: "18%",
            ccsyl: "54%",
            zt: "运行",
            ktsj: "2024-05-01",
            jzsj: "2025-05-01",
          },
          {
            lx: "ECS",
            gg: "8G+512G",
            cpusyl: "33%",
            ncsyl: "18%",
            ccsyl: "54%",
            zt: "运行",
            ktsj: "2024-05-01",
            jzsj: "2025-05-01",
          },
        ],
        relatedIpList: [{ dkh: "***********" }, { dkh: "***********" }],
        securityList: [
          {
            name: "安全事件",
            num: 0,
            icon: require("@/assets/images/monitor/aqsj_blue.png"),
          },
          {
            name: "网络攻击",
            num: 0,
            icon: require("@/assets/images/monitor/wlgj_blue.png"),
          },
          {
            name: "风险隐患",
            num: 0,
            icon: require("@/assets/images/monitor/fxyh_blue.png"),
          },
        ],
        contactsData: {
          xm: "严越",
          jtzz: "江苏省无锡市江阴市夏港街道629号802室",
          zw: "基层",
          lxfs: "333-62752114",
          xb: "男性",
          ssdw: "",
        },
        deptInfoData: {
          dwmc: "金华市委办公室(政研室、档案局)",
          dwfzr: "龚志阳",
          dwgw: "",
          zwzc: "",
          dwlx: "党政机关",
          dh: "13988887963",
          sshy: "政府",
          yx: "333-62752114",
          lsgx: "",
          dwdz: "浙江江省金华市xxxxxxxxxxxx",
          ssjgdw: "市本级",
          jsdw: "金华市数据局",
          gkywcs: "金华市数据局/数据资源处（数据要素产业处）/数据要素组"
        },
        ManuInfoData: {
          kfcs: "",
          kfcstyshxydm: "",
          kfcslxr: "",
          kfcslxdh: "",
          ywcs: "",
          ywcstyshxydm: "",
          ywcslxr: "",
          ywcslxdh: "",
          aqcs: "",
          aqcstyshxydm: "",
          aqcslxr: "",
          aqcslxdh: "",
        },
        SecurityInfoData: {

        },
        fieldsList: [
          {
            name: "金华市基层智治系统-事件日志信息",
            bh: "biz_0700_jhs_event_event_log",
            tag: ["市县级", "市平台已归集", "接口推送"],
            sjydw: "金华市/金华市委社会工作部",
            ssyy: "金华市基层智治系统",
            gjzl: "13,990,988",
            sjfw: "2022年4月至今",
            lyfl: "公共安全",
            zhycgx: "2025-06-02 08:01:15",
            gxpl: "每日",
            tag1: "受限共享",
            tag2: "受限开放",
            tag3: "(市→县)回流",
            fwl: "128",
            sql: "6",
          },
          {
            name: "金华市基层智治系统-事件日志信息",
            bh: "biz_0700_jhs_event_event_log",
            tag: ["市县级", "市平台已归集", "接口推送"],
            sjydw: "金华市/金华市委社会工作部",
            ssyy: "金华市基层智治系统",
            gjzl: "13,990,988",
            sjfw: "2022年4月至今",
            lyfl: "公共安全",
            zhycgx: "2025-06-02 08:01:15",
            gxpl: "每日",
            tag1: "受限共享",
            tag2: "受限开放",
            tag3: "(市→县)回流",
            fwl: "128",
            sql: "6",
          },
        ],
      },
      //基础信息
      jcxx: [
          { name: "应用编码",
            value: "A330701374210202203016675"
          },

          { name: "应用管理员",
            value: "程诗悦"
          },

          { name: "应用类型",
            value: "业务类"
          },

          { name: "上线时间",
            value: "2023/4/1"
          },

          { name: "运维截止时间",
            value: ""
          },

          { name: "运维人员",
            value: ""
          },

          { name: "系统状态",
            value: "运行中"
          },

          { name: "建设依据",
            value: "金委办发〔2020〕33 号"
          },

          { name: "建设层级",
            value: "市级"
          },

          { name: "统建范围",
            value: "市域统建"
          },

          { name: "用户范围",
            value: "本级政府用户,地方各级政府用户"
          },

          { name: "发布端",
            value: "浙政钉,网页"
          },

          { name: "网络环境",
            value: "政务外网"
          },

          { name: "政务服务",
            value: "未涉及政务服务事项业务（含公共服务事项）"
          },

          { name: "所属系统",
            value: "党政机关整体智治,数字政府,数字经济,数字社会"
          },

          { name: "履职领域",
            value: "社会治理"
          },

          { name: "二级领域",
            value: "城市运行管理"
          },

          { name: "应用领域",
            value: "医疗卫生,社保就业,公共安全,城建住房,交通运输,教育文化,科技创新,生态环境,工业农业,商贸流通,安全生产,市场监管,生活服务,气象服务,地理空间"
          },

          { name: "是否协同",
            value: "是"
          },

          { name: "多跨场景",
            value: "是"
          },

          { name: "体系贯通",
            value: "是"
          },
      ],
      //单位信息
      dwxx: [
        { name: "建设单位",
          value: "金华市数据局"
        },

        { name: "归口业务处室",
          value: "金华市数据局/数据资源处（数据要素产业处）/数据要素组"
        },

        { name: "单位负责人",
          value: "程诗悦"
        },

        { name: "单位类型",
          value: "党政机关"
        },

        { name: "电话",
          value: ""
        },

        { name: "单位地址",
          value: ""
        }
      ],
      //厂商信息
      csxx: [
        { name: "开发厂商",
          value: ""
        },

        { name: "开发厂商统一社会信用代码",
          value: ""
        },

        { name: "开发厂商联系人",
          value: ""
        },

        { name: "开发厂商联系电话",
          value: ""
        },

        { name: "运维厂商",
          value: ""
        },

        { name: "运维厂商统一社会信用代码",
          value: ""
        },

        { name: "运维厂商联系人",
          value: ""
        },

        { name: "运维厂商联系电话",
          value: ""
        },

        { name: "安全厂商",
          value: ""
        },

        { name: "安全厂商统一社会信用代码",
          value: ""
        },

        { name: "安全厂商联系人",
          value: ""
        },

        { name: "安全厂商联系电话",
          value: ""
        },
      ],
      //安全信息
      aqxx: [
        {
          name: "等保级别",
          value: "三级"
        },
        {
          name: "是否等保备案",
          value: "是"
        },
        {
          name: "等保备案时间",
          value: "2022/4/25"
        },
        {
          name: "等保备案编号",
          value: "33070099131-22001"
        },
        {
          name: "等保备案机关",
          value: "金华市公安局"
        },
        {
          name: "是否等保测评",
          value: "是"
        },
        {
          name: "等保测评机构",
          value: "浙江安远检测技术有限公司"
        },
        {
          name: "等保测评得分",
          value: "83.76"
        },
        {
          name: "等保测评时间",
          value: "2024/10/25"
        },
        {
          name: "是否密码测评",
          value: "是"
        },
        {
          name: "密码测评级别",
          value: "三级"
        },
        {
          name: "密码测评时间",
          value: "2024/11/7"
        },
        {
          name: "密码测评编号",
          value: "AXJC241648-MP-01"
        },
      ],
      //云资源
      yzyKLlist: [
        {
          key: "zyip",
          label: "资源ip"
        },
        {
          key: "zylx",
          label: "资源类型"
        },
        {
          key: "ycpmc",
          label: "云产品名称"
        },
        {
          key: "sllx",
          label: "实例类型"
        },
        {
          key: "gg",
          label: "规格"
        },
        {
          key: "cpusly",
          label: "CPU使用率"
        },
        {
          key: "ncsyl",
          label: "内存使用率"
        },
        {
          key: "ccsyl",
          label: "存储使用率"
        },
        {
          key: "zt",
          label: "状态"
        },
        {
          key: "ktsj",
          label: "开通时间"
        },
        {
          key: "jzsj",
          label: "截止时间"
        }
      ],
      yzy: [
        {
          zyip:"",
          zylx:"",
          ycpmc:"",
          sllx:"ECS",
          gg: "8G+512G",
          cpusyl: "33%",
          ncsyl: "18%",
          ccsyl: "54%",
          zt: "运行",
          ktsj: "2024-05-01",
          jzsj: "2025-05-01",
        },
        {
          zyip:"",
          zylx:"",
          ycpmc:"",
          sllx:"ECS",
          gg: "8G+512G",
          cpusyl: "33%",
          ncsyl: "18%",
          ccsyl: "54%",
          zt: "运行",
          ktsj: "2024-05-01",
          jzsj: "2025-05-01",
        },
      ],
      //系统地址
      xtdzlist: [
        {
          key: "ym",
          label: "域名"
        },
        {
          key: "ip",
          label: "ip"
        },
        {
          key: "dk",
          label: "端口"
        },
        {
          key: "gxsj",
          label: "更新时间"
        }
      ],
      xtdz: [
        {
          ym: "***********",
          ip: "***********",
          dk: "***********",
          gxsj: "2025-06-10",
        },
        {
          ym: "***********",
          ip: "***********",
          dk: "***********",
          gxsj: "2025-06-10",
        },
      ],
      //关联项目
      glxmlist: [
        {
          key: "xmbm",
          label: "项目编码"
        },
        {
          key: "xmmc",
          label: "项目名称"
        },
        {
          key: "jsbm",
          label: "建设部门"
        },
        {
          key: "lxsj",
          label: "立项时间"
        },
      ],
      glxm: [
        {
          xmbm: "12345678",
          xmmc: "项目名称",
          jsbm: "部门名称",
          lxsj: "2023-11-10",
        },
        {
          xmbm: "12345678",
          xmmc: "项目名称",
          jsbm: "部门名称",
          lxsj: "2023-11-10",
        },
      ],
      //关联ip
      gliplist: [
        {
          key: "ipmc",
          label: "IP名称"
        },
        {
          key: "iph",
          label: "IP号"
        },
        {
          key: "ipzt",
          label: "IP状态"
        }
      ],
      glip: [{ ipmc: "***********",iph: "",ipzt:"" }, { ipmc: "***********",iph: "",ipzt:"" }]
    };
  },
  mounted() {
    window.addEventListener("scroll", this.handleScroll);
  },

  methods: {
    ObjectToArray() {
      return Object.keys(this.data).map((key) => {
        return {
          name: key,
          value: this.data[key],
        };
      });
    },
    changeTab(e) {
      this.tabIndex = e.index;
      const targetId = e.href;
      const target = document.getElementById(targetId);

      if (target) {
        const offset = 12;
        const targetPosition = target.offsetTop - offset;

        window.scrollTo({
          top: targetPosition,
          behavior: "smooth",
        });

        // 更新URL hash（不触发路由跳转）
        history.replaceState(null, null, `#${targetId}`);
      }
    },
    handleScroll() {
      const tabCon = document.querySelector(".tabCon");
      if (window.scrollY > 100) {
        tabCon.classList.add("fixed");
        this.isFixed = true;
      } else {
        tabCon.classList.remove("fixed");
        this.isFixed = false;
      }
    },
    goBack() {
      this.$router.go(-1);
    },
  },
  beforeDestroy() {
    window.removeEventListener("scroll", this.handleScroll);
  },
};
</script>

<style lang="scss" scoped>
.container {
  padding-bottom: 60px;
  box-sizing: border-box;
  position: relative;
  .tabCon {
    position: relative;
    z-index: 10;
    // padding: 0 20px;
    // box-sizing: border-box;
  }
  .tabCon.fixed {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 100;
    padding: 0 20px;
    box-sizing: border-box;
  }
  .tabCon-placeholder {
    width: 100%;
    height: 107px;
  }
}
.card {
  border-radius: 15px;
  padding: 20px 20px;
  box-sizing: border-box;
  background-color: #fff;
  height: auto;
  margin-top: 12px;
  .cardTitle {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 700;
    font-size: 18px;
    color: #1d2129;
    line-height: 24px;
    text-align: left;
    margin-bottom: 10px;
    padding-left: 18px;
    box-sizing: border-box;
    position: relative;
    &::before {
      content: "";
      position: absolute;
      top: 2px;
      left: 0;
      width: 10px;
      height: 20px;
      background: url("~@/assets/images/cardTitle_icon.png");
      background-size: 100% 100%;
    }
  }
}
</style>
