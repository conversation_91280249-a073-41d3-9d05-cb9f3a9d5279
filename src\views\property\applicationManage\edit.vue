<template>
  <div class="container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="flex-c">
        <el-button
          icon="el-icon-arrow-left"
          type="text"
          @click="goBack"
          class="back-btn"
        >
          返回
        </el-button>
        <h2 class="page-title">编辑系统</h2>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="handleSave" size="small">保存</el-button>
        <el-button @click="goBack" size="small">取消</el-button>
      </div>
    </div>

    <!-- 表单内容 -->
    <el-form
      ref="myForm"
      :model="myForm"
      label-width="80px"
      label-position="top"
      inline
      class="edit-form"
    >
      <div class="card">
        <div class="cardTitle">基本信息</div>
        <div class="itemList flex-c">
          <el-form-item label="应用编码" class="item_grid_3">
            <el-input
              v-model="myForm.yybm"
              size="small"
              clearable
              placeholder="请输入应用编码"
            ></el-input>
          </el-form-item>
          <el-form-item label="应用名称" class="item_grid_3">
            <el-input
              v-model="myForm.name"
              size="small"
              clearable
              placeholder="请输入应用名称"
            ></el-input>
          </el-form-item>
          <el-form-item label="应用管理员" class="item_grid_3">
            <el-input
              v-model="myForm.adminId"
              size="small"
              clearable
              placeholder="输入检索已注册人员账号"
            ></el-input>
          </el-form-item>
          <el-form-item label="应用类型" class="item_grid_3">
            <el-select
              v-model="myForm.yylx"
              clearable
              size="small"
              style="width: 100%"
              placeholder="下拉选择"
            >
              <el-option
                v-for="(item, i) in yylxOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="上线时间" class="item_grid_3">
            <el-date-picker
              v-model="myForm.sxsj"
              type="date"
              size="small"
              placeholder="日期选择"
              style="width: 100%"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="运维截止时间" class="item_grid_3">
            <el-date-picker
              v-model="myForm.ywjzsj"
              type="date"
              size="small"
              placeholder="日期选择"
              style="width: 100%"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="主运维人员" class="item_grid_3">
            <el-select
              v-model="myForm.zyywry"
              clearable
              filterable
              remote
              reserve-keyword
              size="small"
              style="width: 100%"
              placeholder="需选择，需先在人员管理页面中维护信息，再在该位置下拉检索运维人员"
              :remote-method="searchYwryList"
              :loading="ywryLoading"
            >
              <el-option
                v-for="item in ywryOptions"
                :key="item.id || item.userId"
                :label="`${item.userName || item.nickName || '未知用户'}`"
                :value="item.id || item.userId"
              >
                <span style="float: left">{{ item.userName || '未设置用户名' }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">{{ item.nickName || '未设置昵称' }}</span>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="辅运维人员" class="item_grid_3">
            <el-select
              v-model="myForm.fyywry"
              clearable
              filterable
              remote
              reserve-keyword
              size="small"
              style="width: 100%"
              placeholder="需选择，需先在人员管理页面中维护信息，再在该位置下拉检索运维人员"
              :remote-method="searchYwryList"
              :loading="ywryLoading"
            >
              <el-option
                v-for="item in ywryOptions"
                :key="item.id || item.userId"
                :label="`${item.userName || item.nickName || '未知用户'}`"
                :value="item.id || item.userId"
              >
                <span style="float: left">{{ item.userName || '未设置用户名' }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">{{ item.nickName || '未设置昵称' }}</span>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="系统状态" class="item_grid_3">
            <el-select
              v-model="myForm.status"
              clearable
              size="small"
              style="width: 100%"
              placeholder="下拉选择"
            >
              <el-option
                v-for="(item, i) in xtztOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="建设依据" class="item_grid_3">
            <el-select
              v-model="myForm.jsyj"
              clearable
              size="small"
              style="width: 100%"
              placeholder="下拉选择"
            >
              <el-option
                v-for="(item, i) in jsyjOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="政策文件" class="item_grid_3">
            <el-input
              v-model="myForm.zcwj"
              size="small"
              clearable
              placeholder="建设依据若选择政策文件则显示政策文件输入框，否则领导讲话及批示输入框"
            ></el-input>
          </el-form-item>
          <el-form-item label="建设层级" class="item_grid_3">
            <el-select
              v-model="myForm.jscj"
              clearable
              size="small"
              style="width: 100%"
              placeholder="下拉选择"
            >
              <el-option
                v-for="(item, i) in jscjOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="是否统建" class="item_grid_3">
            <el-select
              v-model="myForm.sftc"
              clearable
              size="small"
              style="width: 100%"
              placeholder="下拉选择"
            >
              <el-option
                v-for="(item, i) in sftcOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="用户范围" class="item_grid_3">
            <el-select
              v-model="myForm.yhfw"
              clearable
              size="small"
              style="width: 100%"
              placeholder="下拉选择"
            >
              <el-option
                v-for="(item, i) in yhfwOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="发布端" class="item_grid_3">
            <el-select
              v-model="myForm.fbd"
              clearable
              size="small"
              style="width: 100%"
              placeholder="下拉选择"
            >
              <el-option
                v-for="(item, i) in fbdOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="网络环境" class="item_grid_3">
            <el-select
              v-model="myForm.wlhj"
              clearable
              size="small"
              style="width: 100%"
              placeholder="下拉选择"
            >
              <el-option
                v-for="(item, i) in wlhjOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="所属系统" class="item_grid_3">
            <el-select
              v-model="myForm.sshj"
              clearable
              size="small"
              style="width: 100%"
              placeholder="下拉选择"
            >
              <el-option
                v-for="(item, i) in sshjOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="应用领域" class="item_grid_3">
            <el-select
              v-model="myForm.yyly"
              clearable
              size="small"
              style="width: 100%"
              placeholder="下拉选择"
            >
              <el-option
                v-for="(item, i) in yylyOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="是否协同" class="item_grid_3">
            <el-select
              v-model="myForm.isCollaboration"
              clearable
              size="small"
              style="width: 100%"
              placeholder="下拉选择"
            >
              <el-option
                v-for="(item, i) in boolOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="多跨场景" class="item_grid_3">
            <el-select
              v-model="myForm.isCrossScenario"
              clearable
              size="small"
              style="width: 100%"
              placeholder="下拉选择"
            >
              <el-option
                v-for="(item, i) in boolOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="体系贯通" class="item_grid_3">
            <el-select
              v-model="myForm.isSystemConnected"
              clearable
              size="small"
              style="width: 100%"
              placeholder="下拉选择"
            >
              <el-option
                v-for="(item, i) in boolOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="系统地址" class="item_grid_3">
            <el-input
              v-model="myForm.systemUrl"
              size="small"
              clearable
              placeholder="输入"
            ></el-input>
          </el-form-item>
          <el-form-item label="健康监测地址" class="item_grid_3">
            <el-input
              v-model="myForm.url"
              size="small"
              clearable
              placeholder="输入"
            ></el-input>
          </el-form-item>
        </div>
      </div>

      <div class="card">
        <div class="flex-c">
          <div class="cardTitle">系统地址</div>
          <el-button
            icon="el-icon-plus"
            size="small"
            style="margin: 0 0 6px 20px"
            @click="addWebAddress"
          >新增WEB</el-button>
        </div>
        <el-table :data="myForm.xtdzList" border>
          <el-table-column prop="ym" label="域名" align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.ym" size="mini" placeholder="请输入域名"></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="ip" label="IP" align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.ip" size="mini" placeholder="请输入IP"></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="dk" label="端口" align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.dk" size="mini" placeholder="请输入端口"></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="gxsj" label="更新时间" align="center" />
          <el-table-column label="操作" align="center" width="100">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="mini"
                @click="removeWebAddress(scope.$index)"
                style="color: #f56c6c"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
          <template slot="empty">
            <div class="tableEmpty">
              <img src="@/assets/images/property/empty.png" class="img" />
              <span>暂无数据</span>
            </div>
          </template>
        </el-table>
      </div>

      <div class="card">
        <div class="cardTitle">关联项目</div>
        <el-table :data="myForm.glxmList" border>
          <el-table-column prop="xmbm" label="项目编码" align="center" />
          <el-table-column prop="xmmc" label="项目名称" align="center" />
          <el-table-column prop="ssbm" label="所属部门" align="center" />
          <el-table-column prop="lxsj" label="立项时间" align="center" />
          <el-table-column label="操作" align="center">
            <template slot-scope="scope">
              <el-button type="text" size="mini">编辑</el-button>
              <el-button type="text" size="mini" style="color: #f56c6c">删除</el-button>
            </template>
          </el-table-column>
          <template slot="empty">
            <div class="tableEmpty">
              <img src="@/assets/images/property/empty.png" class="img" />
              <span>暂无数据</span>
            </div>
          </template>
        </el-table>
      </div>

      <div class="card">
        <div class="flex-c">
          <div class="cardTitle">关联IP资产</div>
          <el-button size="small" style="margin: 0 0 6px 20px">选择关联资产</el-button>
        </div>
        <el-table :data="myForm.glipzcList" border>
          <el-table-column prop="zcmc" label="资产名称" align="center" />
          <el-table-column prop="zcip" label="资产IP" align="center" />
          <el-table-column prop="zczyx" label="资产重要性" align="center" />
          <el-table-column prop="zczt" label="资产状态" align="center" />
          <el-table-column prop="zcpj" label="资产评级" align="center" />
          <el-table-column label="操作" align="center">
            <template slot-scope="scope">
              <el-button type="text" size="mini" style="color: #f56c6c">移除</el-button>
            </template>
          </el-table-column>
          <template slot="empty">
            <div class="tableEmpty">
              <img src="@/assets/images/property/empty.png" class="img" />
              <span>暂无数据</span>
            </div>
          </template>
        </el-table>
      </div>

      <div class="card">
        <div class="flex-c">
          <div class="cardTitle">联系人</div>
          <el-button
            icon="el-icon-plus"
            size="small"
            style="margin: 0 0 6px 20px"
          >
            新增其他联系人
          </el-button>
        </div>
        <div class="lineTitle">分管联系人</div>
        <div class="itemList flex-c">
          <el-form-item label="联系人" class="item_grid_3">
            <el-input
              v-model="myForm.fglxr['lxr']"
              size="small"
              clearable
              placeholder="请输入"
            ></el-input>
          </el-form-item>
          <el-form-item label="联系方式" class="item_grid_3">
            <el-input
              v-model="myForm.fglxr['lxfs']"
              size="small"
              clearable
              placeholder="请输入"
            ></el-input>
          </el-form-item>
          <el-form-item label="邮箱" class="item_grid_3">
            <el-input
              v-model="myForm.fglxr['yx']"
              size="small"
              clearable
              placeholder="请输入"
            ></el-input>
          </el-form-item>
        </div>
        <div class="lineTitle">部门负责人</div>
        <div class="itemList flex-c">
          <el-form-item label="联系人" class="item_grid_3">
            <el-input
              v-model="myForm.bmfzr['lxr']"
              size="small"
              clearable
              placeholder="请输入"
            ></el-input>
          </el-form-item>
          <el-form-item label="联系方式" class="item_grid_3">
            <el-input
              v-model="myForm.bmfzr['lxfs']"
              size="small"
              clearable
              placeholder="请输入"
            ></el-input>
          </el-form-item>
          <el-form-item label="邮箱" class="item_grid_3">
            <el-input
              v-model="myForm.bmfzr['yx']"
              size="small"
              clearable
              placeholder="请输入"
            ></el-input>
          </el-form-item>
        </div>
        <div class="lineTitle">第一联系人</div>
        <div class="itemList flex-c">
          <el-form-item label="联系人" class="item_grid_3">
            <el-input
              v-model="myForm.dylxr['lxr']"
              size="small"
              clearable
              placeholder="请输入"
            ></el-input>
          </el-form-item>
          <el-form-item label="联系方式" class="item_grid_3">
            <el-input
              v-model="myForm.dylxr['lxfs']"
              size="small"
              clearable
              placeholder="请输入"
            ></el-input>
          </el-form-item>
          <el-form-item label="邮箱" class="item_grid_3">
            <el-input
              v-model="myForm.dylxr['yx']"
              size="small"
              clearable
              placeholder="请输入"
            ></el-input>
          </el-form-item>
        </div>
      </div>
    </el-form>

    <!-- 底部操作栏 -->
    <div class="footer-actions">
      <el-button type="primary" @click="handleSave">保存</el-button>
      <el-button @click="goBack">取消</el-button>
    </div>
  </div>
</template>

<script>
import { listMaintenancePersonnel } from "@/api/property/maintenancePersonnel";

export default {
  name: "ApplicationEdit",
  data() {
    return {
      myForm: {
        yybm: "",
        name: "",
        adminId: "",
        yylx: "",
        sxsj: "",
        ywjzsj: "",
        zyywry: "",
        fyywry: "",
        status: "",
        jsyj: "",
        zcwj: "",
        jscj: "",
        sftc: "",
        yhfw: "",
        fbd: "",
        wlhj: "",
        sshj: "",
        yyly: "",
        isCollaboration: "",
        isCrossScenario: "",
        isSystemConnected: "",
        systemUrl: "",
        url: "",
        xtdzList: [],
        glxmList: [],
        glipzcList: [],
        fglxr: { lxr: "", lxfs: "", yx: "" },
        bmfzr: { lxr: "", lxfs: "", yx: "" },
        dylxr: { lxr: "", lxfs: "", yx: "" },
      },
      boolOptions: [
        { label: "是", value: 1 },
        { label: "否", value: 0 },
      ],
      yylxOptions: [
        { label: "办公后勤类", value: 1 },
        { label: "业务类", value: 2 },
        { label: "信息宣传类", value: 3 },
        { label: "基础支撑类", value: 4 },
      ],
      xtztOptions: [
        { label: "建设中", value: 1 },
        { label: "试运行", value: 2 },
        { label: "运行中", value: 3 },
        { label: "停用", value: 4 },
      ],
      jsyjOptions: [
        { label: "政策文件", value: 1 },
        { label: "领导讲话及批示", value: 2 },
        { label: "部门内部会议/文件", value: 3 },
        { label: "其他", value: 4 },
      ],
      jscjOptions: [
        { label: "国家", value: 1 },
        { label: "省级", value: 2 },
        { label: "市级", value: 3 },
        { label: "县(市、区)", value: 4 },
        { label: "乡镇(街道)", value: 5 },
        { label: "村(社区)", value: 6 },
      ],
      sftcOptions: [
        { label: "是/否", value: 0 },
        { label: "是", value: 1 },
      ],
      yhfwOptions: [
        { label: "处室内部", value: 1 },
        { label: "本部门内部", value: 2 },
        { label: "本级政府用户", value: 3 },
        { label: "地方各级政府用户", value: 4 },
        { label: "社会公众(面向个人)", value: 5 },
        { label: "社会公众(面向法人)", value: 6 },
      ],
      fbdOptions: [
        { label: "浙里办", value: 1 },
        { label: "浙政钉", value: 2 },
        { label: "数字化改革门户", value: 3 },
        { label: "支付宝", value: 4 },
        { label: "微信", value: 5 },
        { label: "网页", value: 6 },
        { label: "园区PC客户端", value: 7 },
        { label: "APP端", value: 8 },
        { label: "政务服务网", value: 9 },
      ],
      wlhjOptions: [
        { label: "是否互联网系统", value: 0 },
        { label: "政务内网", value: 1 },
        { label: "政务外网", value: 2 },
        { label: "互联网", value: 3 },
        { label: "业务专网", value: 4 },
        { label: "单机", value: 5 },
      ],
      sshjOptions: [
        { label: "党政机关整体智治", value: 1 },
        { label: "数字政府", value: 2 },
        { label: "数字经济", value: 3 },
        { label: "数字社会", value: 4 },
        { label: "数字法治", value: 5 },
        { label: "数字文化", value: 6 },
      ],
      yylyOptions: [
        { label: "信用服务", value: 1 },
        { label: "财税金融", value: 2 },
        { label: "医疗卫生", value: 3 },
        { label: "安全生产", value: 4 },
        { label: "社保就业", value: 5 },
        { label: "市场监管", value: 6 },
        { label: "公共安全", value: 7 },
        { label: "社会救助", value: 8 },
        { label: "城建住房", value: 9 },
        { label: "法律服务", value: 10 },
        { label: "交通运输", value: 11 },
        { label: "生活服务", value: 12 },
        { label: "教育文化", value: 13 },
        { label: "气象服务", value: 14 },
        { label: "科技创新", value: 15 },
        { label: "地理空间", value: 16 },
        { label: "资源能源", value: 17 },
        { label: "机构团体", value: 18 },
        { label: "生态环境", value: 19 },
        { label: "其他", value: 20 },
        { label: "工业农业", value: 21 },
        { label: "商贸流通", value: 22 },
      ],
      // 运维人员相关数据
      ywryOptions: [],
      ywryLoading: false,
    };
  },
  created() {
    this.initData();
    this.getYwryList(); // 初始化运维人员列表
  },
  methods: {
    // 初始化数据
    initData() {
      // 从路由参数或者其他方式获取数据
      const formData = this.$route.query.formData || {};
      this.myForm = { ...this.myForm, ...formData };

      // 如果是编辑模式，可以通过ID获取详细数据
      const id = this.$route.query.id;
      if (id) {
        this.loadApplicationData(id);
      }
    },

    // 加载应用数据
    loadApplicationData(id) {
      // 这里应该调用API获取应用详细信息
      console.log("Loading application data for ID:", id);
      // 示例数据
      this.myForm = {
        ...this.myForm,
        yybm: "A330701374210202203016675",
        name: "市委办整体智治门户",
        adminId: "管理员ID",
        yylx: 2,
        sxsj: "2023/4/1",
        ywjzsj: "",
        zyywry: "",
        fyywry: "",
        status: 1,
        jsyj: 1,
        zcwj: "金委办发（2020）33号",
        jscj: 3,
        sftc: 0,
        yhfw: 3,
        fbd: 6,
        wlhj: 2,
        sshj: 1,
        yyly: 1,
        isCollaboration: 0,
        isCrossScenario: 0,
        isSystemConnected: 0,
        systemUrl: "浙政钉网页",
        url: "",
        xtdzList: [
          {
            ym: "dzttzz.swb.jinhua.gov.cn",
            ip: "*************",
            dk: "80",
            gxsj: "2023-12-01"
          }
        ]
      };
    },

    // 新增WEB地址
    addWebAddress() {
      this.myForm.xtdzList.push({
        ym: "",
        ip: "",
        dk: "",
        gxsj: new Date().toISOString().split('T')[0]
      });
    },

    // 删除WEB地址
    removeWebAddress(index) {
      this.$confirm('确定要删除这条WEB地址吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.myForm.xtdzList.splice(index, 1);
        this.$message.success('删除成功');
      }).catch(() => {});
    },

    // 保存数据
    handleSave() {
      this.$refs.myForm.validate((valid) => {
        if (valid) {
          // 这里应该调用API保存数据
          console.log("Saving form data:", this.myForm);
          this.$message.success('保存成功');
          this.goBack();
        } else {
          this.$message.error('请检查表单数据');
        }
      });
    },

    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },

    // 搜索运维人员
    searchYwryList(query) {
      this.ywryLoading = true;
      this.getYwryList(query);
    },

    // 获取运维人员列表
    async getYwryList(keyword = '') {
      try {
        const params = {
          pageNum: 1,
          pageSize: 100, // 增加页面大小以获取更多数据
          status: 1, // 只查询正常状态的人员
        };

        // 如果有关键词，同时按userName和nickName搜索
        if (keyword && keyword.trim()) {
          const trimmedKeyword = keyword.trim();
          // 先尝试按userName搜索
          params.userName = trimmedKeyword;
        }

        console.log('搜索运维人员参数:', params);
        const response = await listMaintenancePersonnel(params);
        console.log('运维人员接口响应:', response);

        if (response.code === 200 && response.data && response.data.list) {
          let list = response.data.list;

          // 如果按userName搜索没有结果，再尝试按nickName搜索
          if (keyword && keyword.trim() && list.length === 0) {
            const nickNameParams = {
              pageNum: 1,
              pageSize: 100,
              status: 1,
              nickName: keyword.trim()
            };
            console.log('按nickName搜索参数:', nickNameParams);
            const nickNameResponse = await listMaintenancePersonnel(nickNameParams);
            console.log('按nickName搜索响应:', nickNameResponse);
            if (nickNameResponse.code === 0 && nickNameResponse.data && nickNameResponse.data.list) {
              list = nickNameResponse.data.list;
            }
          }

          this.ywryOptions = list;
          console.log('最终运维人员列表:', this.ywryOptions);
        } else {
          this.ywryOptions = [];
          console.error('获取运维人员列表失败:', response.msg || response.message);
        }
      } catch (error) {
        console.error('获取运维人员列表异常:', error);
        this.ywryOptions = [];
        this.$message.error('获取运维人员列表失败');
      } finally {
        this.ywryLoading = false;
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.container {
  padding-bottom: 40px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background-color: #fff;
  border-radius: 15px;
  margin-bottom: 12px;

  .back-btn {
    font-size: 16px;
    color: #0057fe;
    margin-right: 12px;

    &:hover {
      color: #003db8;
    }
  }

  .page-title {
    margin: 0;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 700;
    font-size: 20px;
    color: #1d2129;
    line-height: 28px;
  }

  .header-actions {
    display: flex;
    gap: 12px;
  }
}

.edit-form {
  .card {
    border-radius: 15px;
    padding: 20px 20px;
    box-sizing: border-box;
    background-color: #fff;
    height: auto;
    margin-bottom: 12px;

    .cardTitle {
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 700;
      font-size: 18px;
      color: #1d2129;
      line-height: 24px;
      text-align: left;
      margin-bottom: 10px;
      padding-left: 18px;
      box-sizing: border-box;
      position: relative;

      &::before {
        content: "";
        position: absolute;
        top: 2px;
        left: 0;
        width: 10px;
        height: 20px;
        background: url("~@/assets/images/cardTitle_icon.png");
        background-size: 100% 100%;
      }
    }

    .lineTitle {
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 500;
      font-size: 16px;
      color: #1d2129;
      line-height: 24px;
      text-align: left;
      margin: 12px 0 0 32px;
    }
  }
}

.itemList {
  flex-wrap: wrap;
  padding: 0 20px;
  box-sizing: border-box;

  .item_grid_3 {
    width: 30%;
    margin-left: 16px;
    margin-bottom: 20px;
  }
}

.footer-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  border-top: 1px solid #e5e6eb;
  padding: 20px;
  display: flex;
  justify-content: center;
  gap: 12px;
  z-index: 1000;
}

.tableEmpty {
  padding: 10px 0 16px 0;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  color: #1d2129;
  line-height: 22px;
  text-align: center;

  .img {
    width: 82px;
    height: 82px;
    margin-bottom: 6px;
  }
}

// 全局样式覆盖
::v-deep .el-form-item__label {
  padding: 0;
  line-height: 30px;
}

::v-deep .el-form-item {
  margin-bottom: 10px;
}

::v-deep .el-table {
  border-radius: 8px;
  overflow: hidden;
}

::v-deep .el-table th {
  background-color: #f8f9fa;
}
</style>
