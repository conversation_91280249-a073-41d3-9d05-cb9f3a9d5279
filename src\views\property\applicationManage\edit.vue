<template>
  <div class="container" v-loading="loading" element-loading-text="加载中...">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="flex-c">
        <el-button
          icon="el-icon-arrow-left"
          type="text"
          @click="goBack"
          class="back-btn"
        >
          返回
        </el-button>
        <h2 class="page-title">编辑系统</h2>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="handleSave" size="small">保存</el-button>
        <el-button @click="goBack" size="small">取消</el-button>
      </div>
    </div>

    <!-- 表单内容 -->
    <el-form
      ref="myForm"
      :model="myForm"
      :rules="formRules"
      label-width="80px"
      label-position="top"
      inline
      class="edit-form"
    >
      <div class="card">
        <div class="cardTitle">基本信息</div>
        <div class="itemList flex-c">
          <el-form-item label="应用编码" prop="yybm" class="item_grid_3">
            <el-input
              v-model="myForm.yybm"
              size="small"
              clearable
              placeholder="请输入应用编码"
            ></el-input>
          </el-form-item>
          <el-form-item label="应用名称" prop="name" class="item_grid_3">
            <el-input
              v-model="myForm.name"
              size="small"
              clearable
              placeholder="请输入应用名称"
            ></el-input>
          </el-form-item>
          <el-form-item label="应用管理员" class="item_grid_3">
            <el-input
              v-model="myForm.adminId"
              size="small"
              clearable
              placeholder="输入检索已注册人员账号"
            ></el-input>
          </el-form-item>
          <el-form-item label="应用类型" prop="yylx" class="item_grid_3">
            <el-select
              v-model="myForm.yylx"
              clearable
              size="small"
              style="width: 100%"
              placeholder="下拉选择"
            >
              <el-option
                v-for="(item, i) in yylxOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="上线时间" class="item_grid_3">
            <el-date-picker
              v-model="myForm.sxsj"
              type="date"
              size="small"
              placeholder="日期选择"
              style="width: 100%"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="运维截止时间" class="item_grid_3">
            <el-date-picker
              v-model="myForm.ywjzsj"
              type="date"
              size="small"
              placeholder="日期选择"
              style="width: 100%"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="主运维人员" class="item_grid_3">
            <el-select
              v-model="myForm.zyywry"
              clearable
              filterable
              remote
              reserve-keyword
              size="small"
              style="width: 100%"
              placeholder="输入以检索运维人员"
              :remote-method="searchYwryList"
              :loading="ywryLoading"
            >
              <el-option
                v-for="item in ywryOptions"
                :key="item.id || item.userId"
                :label="`${item.userName || item.nickName || '未知用户'}`"
                :value="item.id || item.userId"
              >
                <span style="float: left">{{ item.userName || '未设置用户名' }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">{{ item.nickName || '未设置昵称' }}</span>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="辅运维人员" class="item_grid_3">
            <el-select
              v-model="myForm.fyywry"
              clearable
              filterable
              remote
              reserve-keyword
              size="small"
              style="width: 100%"
              placeholder="输入以检索运维人员"
              :remote-method="searchYwryList"
              :loading="ywryLoading"
            >
              <el-option
                v-for="item in ywryOptions"
                :key="item.id || item.userId"
                :label="`${item.userName || item.nickName || '未知用户'}`"
                :value="item.id || item.userId"
              >
                <span style="float: left">{{ item.userName || '未设置用户名' }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">{{ item.nickName || '未设置昵称' }}</span>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="系统状态" prop="status" class="item_grid_3">
            <el-select
              v-model="myForm.status"
              clearable
              size="small"
              style="width: 100%"
              placeholder="下拉选择"
            >
              <el-option
                v-for="(item, i) in xtztOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="建设依据" class="item_grid_3">
            <el-select
              v-model="myForm.jsyj"
              clearable
              size="small"
              style="width: 100%"
              placeholder="下拉选择"
            >
              <el-option
                v-for="(item, i) in jsyjOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="政策文件" class="item_grid_3">
            <el-input
              v-model="myForm.zcwj"
              size="small"
              clearable
              placeholder="建设依据若选择政策文件则显示政策文件输入框，否则领导讲话及批示输入框"
            ></el-input>
          </el-form-item>
          <el-form-item label="建设层级" class="item_grid_3">
            <el-select
              v-model="myForm.jscj"
              clearable
              size="small"
              style="width: 100%"
              placeholder="下拉选择"
            >
              <el-option
                v-for="(item, i) in jscjOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="是否统建" class="item_grid_3">
            <el-select
              v-model="myForm.sftc"
              clearable
              size="small"
              style="width: 100%"
              placeholder="下拉选择"
            >
              <el-option
                v-for="(item, i) in sftcOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="用户范围" class="item_grid_3">
            <el-select
              v-model="myForm.yhfw"
              clearable
              size="small"
              style="width: 100%"
              placeholder="下拉选择"
            >
              <el-option
                v-for="(item, i) in yhfwOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="发布端" class="item_grid_3">
            <el-select
              v-model="myForm.fbd"
              clearable
              size="small"
              style="width: 100%"
              placeholder="下拉选择"
            >
              <el-option
                v-for="(item, i) in fbdOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="网络环境" class="item_grid_3">
            <el-select
              v-model="myForm.wlhj"
              clearable
              size="small"
              style="width: 100%"
              placeholder="下拉选择"
            >
              <el-option
                v-for="(item, i) in wlhjOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="所属系统" class="item_grid_3">
            <el-select
              v-model="myForm.sshj"
              clearable
              size="small"
              style="width: 100%"
              placeholder="下拉选择"
            >
              <el-option
                v-for="(item, i) in sshjOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="应用领域" class="item_grid_3">
            <el-select
              v-model="myForm.yyly"
              clearable
              size="small"
              style="width: 100%"
              placeholder="下拉选择"
            >
              <el-option
                v-for="(item, i) in yylyOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="是否协同" class="item_grid_3">
            <el-select
              v-model="myForm.isCollaboration"
              clearable
              size="small"
              style="width: 100%"
              placeholder="下拉选择"
            >
              <el-option
                v-for="(item, i) in boolOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="多跨场景" class="item_grid_3">
            <el-select
              v-model="myForm.isCrossScenario"
              clearable
              size="small"
              style="width: 100%"
              placeholder="下拉选择"
            >
              <el-option
                v-for="(item, i) in boolOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="体系贯通" class="item_grid_3">
            <el-select
              v-model="myForm.isSystemConnected"
              clearable
              size="small"
              style="width: 100%"
              placeholder="下拉选择"
            >
              <el-option
                v-for="(item, i) in boolOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="系统地址" class="item_grid_3">
            <el-input
              v-model="myForm.systemUrl"
              size="small"
              clearable
              placeholder="输入"
            ></el-input>
          </el-form-item>
          <el-form-item label="健康监测地址" class="item_grid_3">
            <el-input
              v-model="myForm.url"
              size="small"
              clearable
              placeholder="输入"
            ></el-input>
          </el-form-item>
        </div>
      </div>

      <div class="card">
        <div class="cardTitle">安全信息</div>
        <div class="itemList flex-c">
          <el-form-item label="等保级别" class="item_grid_3">
            <el-select
              v-model="myForm.dbSecurityLevel"
              clearable
              size="small"
              style="width: 100%"
              placeholder="下拉选择"
            >
              <el-option
                v-for="(item, i) in dbSecurityLevelOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="是否等保备案" class="item_grid_3">
            <el-select
              v-model="myForm.dbIsSecurityFiling"
              clearable
              size="small"
              style="width: 100%"
              placeholder="下拉选择"
            >
              <el-option
                v-for="(item, i) in boolOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="等保备案时间" class="item_grid_3">
            <el-date-picker
              v-model="myForm.dbSecurityFilingDate"
              type="date"
              size="small"
              placeholder="时间选择"
              style="width: 100%"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="等保备案编号" class="item_grid_3">
            <el-input
              v-model="myForm.dbSecurityFilingNo"
              size="small"
              clearable
              placeholder="输入"
            ></el-input>
          </el-form-item>
          <el-form-item label="等保备案机关" class="item_grid_3">
            <el-input
              v-model="myForm.dbSecurityFilingOrg"
              size="small"
              clearable
              placeholder="输入"
            ></el-input>
          </el-form-item>
          <el-form-item label="是否等保测评" class="item_grid_3">
            <el-select
              v-model="myForm.dbIsSecurityAssessment"
              clearable
              size="small"
              style="width: 100%"
              placeholder="下拉选择"
            >
              <el-option
                v-for="(item, i) in boolOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="等保测评机构" class="item_grid_3">
            <el-input
              v-model="myForm.dbSecurityAssessmentOrg"
              size="small"
              clearable
              placeholder="输入"
            ></el-input>
          </el-form-item>
          <el-form-item label="等保测评得分" class="item_grid_3">
            <el-input
              v-model="myForm.dbSecurityAssessmentScore"
              size="small"
              clearable
              placeholder="输入"
            ></el-input>
          </el-form-item>
          <el-form-item label="等保测评时间" class="item_grid_3">
            <el-date-picker
              v-model="myForm.dbSecurityAssessmentDate"
              type="date"
              size="small"
              placeholder="时间选择"
              style="width: 100%"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="是否密码测评" class="item_grid_3">
            <el-select
              v-model="myForm.dbIsCryptoAssessment"
              clearable
              size="small"
              style="width: 100%"
              placeholder="下拉选择"
            >
              <el-option
                v-for="(item, i) in boolOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="密码测评级别" class="item_grid_3">
            <el-select
              v-model="myForm.dbCryptoAssessmentLevel"
              clearable
              size="small"
              style="width: 100%"
              placeholder="下拉选择"
            >
              <el-option
                v-for="(item, i) in dbCryptoAssessmentLevelOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="密码测评时间" class="item_grid_3">
            <el-date-picker
              v-model="myForm.dbCryptoAssessmentDate"
              type="date"
              size="small"
              placeholder="时间选择"
              style="width: 100%"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="密码测评编号" class="item_grid_3">
            <el-input
              v-model="myForm.dbCryptoAssessmentNo"
              size="small"
              clearable
              placeholder="输入"
            ></el-input>
          </el-form-item>
        </div>
      </div>

      <div class="card">
        <div class="flex-c">
          <div class="cardTitle">云资源</div>
          <el-button
            icon="el-icon-plus"
            size="small"
            style="margin: 0 0 6px 20px"
            @click="addCloudResource"
          >新增云资源</el-button>
        </div>
        <el-table :data="myForm.yzytList" border>
          <el-table-column prop="ip" label="资源IP" align="center">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.ip"
                size="small"
                placeholder="输入"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="zylx" label="资源类型" align="center">
            <template slot-scope="scope">
              <el-select
                v-model="scope.row.zylx"
                size="small"
                placeholder="下拉选择"
                style="width: 100%"
              >
                <el-option
                  v-for="(item, i) in cloudResourceTypeOptions"
                  :key="i"
                  :value="item.value"
                  :label="item.label"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="ycpmc" label="云产品名称" align="center">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.ycpmc"
                size="small"
                placeholder="输入"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="sllx" label="实例类型" align="center">
            <template slot-scope="scope">
              <el-select
                v-model="scope.row.sllx"
                size="small"
                placeholder="下拉选择"
                style="width: 100%"
              >
                <el-option
                  v-for="(item, i) in instanceTypeOptions"
                  :key="i"
                  :value="item.value"
                  :label="item.label"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="gg" label="规格" align="center">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.gg"
                size="small"
                placeholder="输入"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="ktsj" label="开通时间" align="center">
            <template slot-scope="scope">
              <el-date-picker
                v-model="scope.row.ktsj"
                type="date"
                size="small"
                placeholder="时间选择"
                style="width: 100%"
              ></el-date-picker>
            </template>
          </el-table-column>
          <el-table-column prop="jzsj" label="截止时间" align="center">
            <template slot-scope="scope">
              <el-date-picker
                v-model="scope.row.jzsj"
                type="date"
                size="small"
                placeholder="时间选择"
                style="width: 100%"
              ></el-date-picker>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="100">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                @click="removeCloudResource(scope.$index)"
              >删除</el-button>
            </template>
          </el-table-column>
          <template slot="empty">
            <div class="empty">
              <img src="@/assets/images/property/empty.png" class="img" />
              <span>暂无数据</span>
            </div>
          </template>
        </el-table>
      </div>

      <div class="card">
        <div class="cardTitle">单位信息</div>
        <div class="itemList flex-c">
          <el-form-item label="建设单位" class="item_grid_3">
            <el-input
              v-model="myForm.constructionUnitId"
              size="small"
              clearable
              placeholder="输入检索"
            ></el-input>
          </el-form-item>
          <el-form-item label="归口业务处室" class="item_grid_3">
            <el-input
              v-model="myForm.businessDepartment"
              size="small"
              clearable
              placeholder="输入"
            ></el-input>
          </el-form-item>
          <el-form-item label="单位负责人" class="item_grid_3">
            <el-input
              v-model="myForm.unitLeader"
              size="small"
              clearable
              placeholder="输入"
            ></el-input>
          </el-form-item>
          <el-form-item label="单位类型" class="item_grid_3">
            <el-select
              v-model="myForm.unitType"
              clearable
              size="small"
              style="width: 100%"
              placeholder="下拉选择"
            >
              <el-option
                v-for="(item, i) in unitTypeOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="电话" class="item_grid_3">
            <el-input
              v-model="myForm.phone"
              size="small"
              clearable
              placeholder="输入"
            ></el-input>
          </el-form-item>
          <el-form-item label="单位地址" class="item_grid_3">
            <el-input
              v-model="myForm.address"
              size="small"
              clearable
              placeholder="输入"
            ></el-input>
          </el-form-item>
        </div>
      </div>

      <div class="card">
        <div class="cardTitle">厂商信息</div>
        <div class="itemList flex-c">
          <el-form-item label="开发厂商" class="item_grid_3">
            <el-select
              v-model="myForm.csDeveloperId"
              clearable
              filterable
              remote
              :remote-method="searchDeveloperVendors"
              :loading="developerVendorLoading"
              size="small"
              style="width: 100%"
              placeholder="需先在供应商管理页面中维护信息，再在该位置下拉检索供应商"
            >
              <el-option
                v-for="item in developerVendorOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="运维厂商" class="item_grid_3">
            <el-select
              v-model="myForm.csOperatorId"
              clearable
              filterable
              remote
              :remote-method="searchOperatorVendors"
              :loading="operatorVendorLoading"
              size="small"
              style="width: 100%"
              placeholder="需先在供应商管理页面中维护信息，再在该位置下拉检索供应商"
            >
              <el-option
                v-for="item in operatorVendorOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="安全厂商" class="item_grid_3">
            <el-select
              v-model="myForm.csSecurityVendorId"
              clearable
              filterable
              remote
              :remote-method="searchSecurityVendors"
              :loading="securityVendorLoading"
              size="small"
              style="width: 100%"
              placeholder="需先在供应商管理页面中维护信息，再在该位置下拉检索供应商"
            >
              <el-option
                v-for="item in securityVendorOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="开发厂商统一社会信用代码" class="item_grid_3">
            <el-input
              v-model="myForm.developerVendorCode"
              size="small"
              clearable
              placeholder="自动带出"
              :disabled="true"
            ></el-input>
          </el-form-item>
          <el-form-item label="运维厂商统一社会信用代码" class="item_grid_3">
            <el-input
              v-model="myForm.operatorVendorCode"
              size="small"
              clearable
              placeholder="自动带出"
              :disabled="true"
            ></el-input>
          </el-form-item>
          <el-form-item label="安全厂商统一社会信用代码" class="item_grid_3">
            <el-input
              v-model="myForm.securityVendorCode"
              size="small"
              clearable
              placeholder="自动带出"
              :disabled="true"
            ></el-input>
          </el-form-item>

          <el-form-item label="开发厂商联系人" class="item_grid_3">
            <el-select
              v-model="myForm.csDeveloperUserId"
              clearable
              size="small"
              style="width: 100%"
              placeholder="选择该厂商人员"
            >
              <el-option
                v-for="item in developerContactOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="运维厂商联系人" class="item_grid_3">
            <el-select
              v-model="myForm.csOperatorContactId"
              clearable
              size="small"
              style="width: 100%"
              placeholder="输入"
            >
              <el-option
                v-for="item in operatorContactOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="安全厂商联系人" class="item_grid_3">
            <el-select
              v-model="myForm.csSecurityVendorContactId"
              clearable
              size="small"
              style="width: 100%"
              placeholder="输入"
            >
              <el-option
                v-for="item in securityContactOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="开发厂商联系电话" class="item_grid_3">
            <el-input
              v-model="myForm.developerContactPhone"
              size="small"
              clearable
              placeholder="自动带出"
              :disabled="true"
            ></el-input>
          </el-form-item>
          <el-form-item label="运维厂商联系电话" class="item_grid_3">
            <el-input
              v-model="myForm.operatorContactPhone"
              size="small"
              clearable
              placeholder="自动带出"
              disabled
            ></el-input>
          </el-form-item>
          <el-form-item label="安全厂商联系电话" class="item_grid_3">
            <el-input
              v-model="myForm.securityContactPhone"
              size="small"
              clearable
              placeholder="自动带出"
              disabled
            ></el-input>
          </el-form-item>
        </div>
      </div>

      <div class="card">
        <div class="flex-c">
          <div class="cardTitle">关联项目</div>
          <el-button
            icon="el-icon-plus"
            size="small"
            style="margin: 0 0 6px 20px"
            @click="addRelatedProject"
          >新增关联项目</el-button>
        </div>
        <el-table :data="myForm.xmList" border>
          <el-table-column prop="xmbm" label="项目编码" align="center">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.xmbm"
                size="small"
                placeholder="输入"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="xmmc" label="项目名称" align="center">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.xmmc"
                size="small"
                placeholder="输入"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="jsbm" label="建设部门" align="center">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.jsbm"
                size="small"
                placeholder="输入"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="lxsj" label="立项时间" align="center">
            <template slot-scope="scope">
              <el-date-picker
                v-model="scope.row.lxsj"
                type="date"
                size="small"
                placeholder="时间选择"
                style="width: 100%"
              ></el-date-picker>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="100">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                @click="removeRelatedProject(scope.$index)"
              >删除</el-button>
            </template>
          </el-table-column>
          <template slot="empty">
            <div class="empty">
              <img src="@/assets/images/property/empty.png" class="img" />
              <span>暂无数据</span>
            </div>
          </template>
        </el-table>
      </div>

      <div class="card">
        <div class="flex-c">
          <div class="cardTitle">关联IP</div>
          <el-button
            icon="el-icon-plus"
            size="small"
            style="margin: 0 0 6px 20px"
            @click="addRelatedIp"
          >新增关联IP</el-button>
        </div>
        <el-table :data="myForm.ipList" border>
          <el-table-column prop="ipName" label="IP名称" align="center">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.ipName"
                size="small"
                placeholder="输入"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="ip" label="IP号" align="center">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.ip"
                size="small"
                placeholder="输入"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="dkh" label="端口号" align="center">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.dkh"
                size="small"
                placeholder="输入"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="ipStatus" label="IP状态" align="center">
            <template slot-scope="scope">
              <el-select
                v-model="scope.row.ipStatus"
                size="small"
                placeholder="下拉选择"
                style="width: 100%"
              >
                <el-option
                  v-for="(item, i) in ipStatusOptions"
                  :key="i"
                  :value="item.value"
                  :label="item.label"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="100">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                @click="removeRelatedIp(scope.$index)"
              >删除</el-button>
            </template>
          </el-table-column>
          <template slot="empty">
            <div class="empty">
              <img src="@/assets/images/property/empty.png" class="img" />
              <span>暂无数据</span>
            </div>
          </template>
        </el-table>
      </div>

      <div class="card">
        <div class="flex-c">
          <div class="cardTitle">使用组件</div>
          <el-button
            icon="el-icon-plus"
            size="small"
            style="margin: 0 0 6px 20px"
            @click="addComponent"
          >新增使用组件</el-button>
        </div>
        <el-table :data="myForm.zjList" border>
          <el-table-column prop="type" label="类型" align="center">
            <template slot-scope="scope">
              <el-select
                v-model="scope.row.type"
                size="small"
                placeholder="下拉选择"
                style="width: 100%"
              >
                <el-option
                  v-for="(item, i) in componentTypeOptions"
                  :key="i"
                  :value="item.value"
                  :label="item.label"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="gylId" label="名称" align="center">
            <template slot-scope="scope">
              <el-select
                v-model="scope.row.gylId"
                clearable
                filterable
                remote
                :remote-method="searchComponents"
                :loading="componentLoading"
                size="small"
                style="width: 100%"
                placeholder="输入检索"
              >
                <el-option
                  v-for="item in componentOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="gys" label="供应商" align="center">
            <template slot-scope="scope">
              <el-select
                v-model="scope.row.gys"
                clearable
                filterable
                remote
                :remote-method="searchSupplierForComponent"
                :loading="supplierLoading"
                size="small"
                style="width: 100%"
                placeholder="输入检索"
              >
                <el-option
                  v-for="item in supplierOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.name"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="bbh" label="版本号" align="center">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.bbh"
                size="small"
                placeholder="自动带出"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="100">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                @click="removeComponent(scope.$index)"
              >删除</el-button>
            </template>
          </el-table-column>
          <template slot="empty">
            <div class="empty">
              <img src="@/assets/images/property/empty.png" class="img" />
              <span>暂无数据</span>
            </div>
          </template>
        </el-table>
      </div>
    </el-form>

    <!-- 底部操作栏 -->
    <div class="footer-actions">
      <el-button type="primary" @click="handleSave">保存</el-button>
      <el-button @click="goBack">取消</el-button>
    </div>
  </div>
</template>

<script>
import { listMaintenancePersonnel, listContactsBySupplier } from "@/api/property/maintenancePersonnel";
import { listSupplier } from "@/api/property/supplierManage";
import { getApplication, updateApplication } from "@/api/property/applicationManage";

export default {
  name: "ApplicationEdit",
  data() {
    return {
      myForm: {
        yybm: "",
        name: "",
        adminId: "",
        yylx: "",
        sxsj: "",
        ywjzsj: "",
        zyywry: "",
        fyywry: "",
        status: "",
        jsyj: "",
        zcwj: "",
        jscj: "",
        sftc: "",
        yhfw: "",
        fbd: "",
        wlhj: "",
        sshj: "",
        yyly: "",
        isCollaboration: "",
        isCrossScenario: "",
        isSystemConnected: "",
        systemUrl: "",
        url: "",
        // 安全信息字段
        dbSecurityLevel: "", // 等保级别
        dbIsSecurityFiling: "", // 是否等保备案
        dbSecurityFilingDate: "", // 等保备案时间
        dbSecurityFilingNo: "", // 等保备案编号
        dbSecurityFilingOrg: "", // 等保备案机关
        dbIsSecurityAssessment: "", // 是否等保测评
        dbSecurityAssessmentOrg: "", // 等保测评机构
        dbSecurityAssessmentScore: "", // 等保测评得分
        dbSecurityAssessmentDate: "", // 等保测评时间
        dbIsCryptoAssessment: "", // 是否密码测评
        dbCryptoAssessmentLevel: "", // 密码测评级别
        dbCryptoAssessmentDate: "", // 密码测评时间
        dbCryptoAssessmentNo: "", // 密码测评编号
        // 云资源列表
        yzytList: [],
        // 单位信息字段
        constructionUnitId: "", // 建设单位
        businessDepartment: "", // 归口业务处室
        unitLeader: "", // 单位负责人
        unitType: "", // 单位类型
        phone: "", // 电话
        address: "", // 单位地址
        // 厂商信息字段
        csDeveloperId: "", // 开发厂商id
        developerVendorCode: "", // 开发厂商统一社会信用代码
        csDeveloperUserId: "", // 开发厂商联系人id
        developerContactPhone: "", // 开发厂商联系电话
        csOperatorId: "", // 运维厂商id
        operatorVendorCode: "", // 运维厂商统一社会信用代码
        csOperatorContactId: "", // 运维厂商联系人id
        operatorContactPhone: "", // 运维厂商联系电话
        csSecurityVendorId: "", // 安全厂商id
        securityVendorCode: "", // 安全厂商统一社会信用代码
        csSecurityVendorContactId: "", // 安全厂商联系人id
        securityContactPhone: "", // 安全厂商联系电话
        // 关联项目列表
        xmList: [],
        // 关联IP列表
        ipList: [],
        // 使用组件列表
        zjList: [],
      },
      boolOptions: [
        { label: "是", value: 1 },
        { label: "否", value: 0 },
      ],
      yylxOptions: [
        { label: "办公后勤类", value: 1 },
        { label: "业务类", value: 2 },
        { label: "信息宣传类", value: 3 },
        { label: "基础支撑类", value: 4 },
      ],
      xtztOptions: [
        { label: "建设中", value: 1 },
        { label: "试运行", value: 2 },
        { label: "运行中", value: 3 },
        { label: "停用", value: 4 },
      ],
      jsyjOptions: [
        { label: "政策文件", value: 1 },
        { label: "领导讲话及批示", value: 2 },
        { label: "部门内部会议/文件", value: 3 },
        { label: "其他", value: 4 },
      ],
      jscjOptions: [
        { label: "国家", value: 1 },
        { label: "省级", value: 2 },
        { label: "市级", value: 3 },
        { label: "县(市、区)", value: 4 },
        { label: "乡镇(街道)", value: 5 },
        { label: "村(社区)", value: 6 },
      ],
      sftcOptions: [
        { label: "否", value: 0 },
        { label: "是", value: 1 },
      ],
      yhfwOptions: [
        { label: "处室内部", value: 1 },
        { label: "本部门内部", value: 2 },
        { label: "本级政府用户", value: 3 },
        { label: "地方各级政府用户", value: 4 },
        { label: "社会公众(面向个人)", value: 5 },
        { label: "社会公众(面向法人)", value: 6 },
      ],
      fbdOptions: [
        { label: "浙里办", value: 1 },
        { label: "浙政钉", value: 2 },
        { label: "数字化改革门户", value: 3 },
        { label: "支付宝", value: 4 },
        { label: "微信", value: 5 },
        { label: "网页", value: 6 },
        { label: "园区PC客户端", value: 7 },
        { label: "APP端", value: 8 },
        { label: "政务服务网", value: 9 },
      ],
      wlhjOptions: [
        { label: "是否互联网系统", value: 0 },
        { label: "政务内网", value: 1 },
        { label: "政务外网", value: 2 },
        { label: "互联网", value: 3 },
        { label: "业务专网", value: 4 },
        { label: "单机", value: 5 },
      ],
      sshjOptions: [
        { label: "党政机关整体智治", value: 1 },
        { label: "数字政府", value: 2 },
        { label: "数字经济", value: 3 },
        { label: "数字社会", value: 4 },
        { label: "数字法治", value: 5 },
        { label: "数字文化", value: 6 },
      ],
      yylyOptions: [
        { label: "信用服务", value: 1 },
        { label: "财税金融", value: 2 },
        { label: "医疗卫生", value: 3 },
        { label: "安全生产", value: 4 },
        { label: "社保就业", value: 5 },
        { label: "市场监管", value: 6 },
        { label: "公共安全", value: 7 },
        { label: "社会救助", value: 8 },
        { label: "城建住房", value: 9 },
        { label: "法律服务", value: 10 },
        { label: "交通运输", value: 11 },
        { label: "生活服务", value: 12 },
        { label: "教育文化", value: 13 },
        { label: "气象服务", value: 14 },
        { label: "科技创新", value: 15 },
        { label: "地理空间", value: 16 },
        { label: "资源能源", value: 17 },
        { label: "机构团体", value: 18 },
        { label: "生态环境", value: 19 },
        { label: "其他", value: 20 },
        { label: "工业农业", value: 21 },
        { label: "商贸流通", value: 22 },
      ],
      // 等保级别选项
      dbSecurityLevelOptions: [
        { label: "一级", value: 1 },
        { label: "二级", value: 2 },
        { label: "三级", value: 3 },
      ],
      // 密码测评级别选项
      dbCryptoAssessmentLevelOptions: [
        { label: "一级", value: 1 },
        { label: "二级", value: 2 },
        { label: "三级", value: 3 },
        { label: "四级", value: 4 },
        { label: "五级", value: 5 },
      ],
      // 云资源类型选项
      cloudResourceTypeOptions: [
        { label: "计算服务", value: "计算服务" },
        { label: "数据库服务", value: "数据库服务" },
        { label: "存储服务", value: "存储服务" },
        { label: "网络服务", value: "网络服务" },
        { label: "基础服务", value: "基础服务" },
      ],
      // 实例类型选项
      instanceTypeOptions: [
        { label: "ECS", value: "ECS" },
        { label: "RDS", value: "RDS" },
        { label: "Redis", value: "Redis" },
        { label: "NAS", value: "NAS" },
        { label: "EIP", value: "EIP" },
        { label: "ECS_SC", value: "ECS_SC" },
        { label: "CSA", value: "CSA" },
        { label: "FM", value: "FM" },
      ],
      // 单位类型选项
      unitTypeOptions: [
        { label: "党政机关", value: "党政机关" },
        { label: "国企", value: "国企" },
      ],
      // IP状态选项
      ipStatusOptions: [
        { label: "运行", value: 1 },
        { label: "关闭", value: 2 },
      ],
      // 组件类型选项
      componentTypeOptions: [
        { label: "中间件", value: 1 },
        { label: "系统", value: 2 },
        { label: "数据库", value: 3 },
      ],
      // 组件相关数据
      componentOptions: [],
      componentLoading: false,
      // 供应商相关数据（用于组件模块）
      supplierOptions: [],
      supplierLoading: false,
      // 厂商相关数据
      developerVendorOptions: [],
      developerVendorLoading: false,
      developerContactOptions: [],
      operatorVendorOptions: [],
      operatorVendorLoading: false,
      operatorContactOptions: [],
      securityVendorOptions: [],
      securityVendorLoading: false,
      securityContactOptions: [],
      // 运维人员相关数据
      ywryOptions: [],
      ywryLoading: false,
      // 页面加载状态
      loading: false,
      // 表单验证规则
      formRules: {
        name: [
          { required: true, message: '请输入应用名称', trigger: 'blur' }
        ],
        yybm: [
          { required: true, message: '请输入应用编码', trigger: 'blur' }
        ],
        yylx: [
          { required: true, message: '请选择应用类型', trigger: 'change' }
        ],
        status: [
          { required: true, message: '请选择系统状态', trigger: 'change' }
        ]
      },
    };
  },
  watch: {
    // 监听开发厂商选择变化
    'myForm.csDeveloperId'(newVal) {
      if (newVal) {
        // 从选项中找到对应的厂商信息
        const selectedVendor = this.developerVendorOptions.find(item => item.id === newVal);
        if (selectedVendor) {
          this.myForm.developerVendorCode = selectedVendor.code || '';
        }
        // 调用API获取该厂商的联系人列表
        this.loadDeveloperContacts(newVal);
      } else {
        this.myForm.developerVendorCode = '';
        this.developerContactOptions = [];
        this.myForm.csDeveloperUserId = '';
        this.myForm.developerContactPhone = '';
      }
    },
    // 监听开发厂商联系人选择变化
    'myForm.csDeveloperUserId'(newVal) {
      if (newVal) {
        const contact = this.developerContactOptions.find(item => item.id === newVal);
        this.myForm.developerContactPhone = contact ? contact.phone : '';
      } else {
        this.myForm.developerContactPhone = '';
      }
    },
    // 监听运维厂商选择变化
    'myForm.csOperatorId'(newVal) {
      if (newVal) {
        // 从选项中找到对应的厂商信息
        const selectedVendor = this.operatorVendorOptions.find(item => item.id === newVal);
        if (selectedVendor) {
          this.myForm.operatorVendorCode = selectedVendor.code || '';
        }
        // 调用API获取该厂商的联系人列表
        this.loadOperatorContacts(newVal);
      } else {
        this.myForm.operatorVendorCode = '';
        this.operatorContactOptions = [];
        this.myForm.csOperatorContactId = '';
        this.myForm.operatorContactPhone = '';
      }
    },
    // 监听运维厂商联系人选择变化
    'myForm.csOperatorContactId'(newVal) {
      if (newVal) {
        const contact = this.operatorContactOptions.find(item => item.id === newVal);
        this.myForm.operatorContactPhone = contact ? contact.phone : '';
      } else {
        this.myForm.operatorContactPhone = '';
      }
    },
    // 监听安全厂商选择变化
    'myForm.csSecurityVendorId'(newVal) {
      if (newVal) {
        // 从选项中找到对应的厂商信息
        const selectedVendor = this.securityVendorOptions.find(item => item.id === newVal);
        if (selectedVendor) {
          this.myForm.securityVendorCode = selectedVendor.code || '';
        }
        // 调用API获取该厂商的联系人列表
        this.loadSecurityContacts(newVal);
      } else {
        this.myForm.securityVendorCode = '';
        this.securityContactOptions = [];
        this.myForm.csSecurityVendorContactId = '';
        this.myForm.securityContactPhone = '';
      }
    },
    // 监听安全厂商联系人选择变化
    'myForm.csSecurityVendorContactId'(newVal) {
      if (newVal) {
        const contact = this.securityContactOptions.find(item => item.id === newVal);
        this.myForm.securityContactPhone = contact ? contact.phone : '';
      } else {
        this.myForm.securityContactPhone = '';
      }
    },
    // 监听组件列表变化，自动填充供应商和版本号
    'myForm.zjList': {
      handler(newVal) {
        newVal.forEach((item, index) => {
          if (item.gylId && !item.gys && !item.bbh) {
            // 从组件选项中找到对应的组件信息
            const selectedComponent = this.componentOptions.find(comp => comp.id === item.gylId);
            if (selectedComponent) {
              this.$set(this.myForm.zjList, index, {
                ...item,
                gys: selectedComponent.supplier,
                bbh: selectedComponent.version
              });
            }
          }
        });
      },
      deep: true
    },
  },
  created() {
    this.initData();
    this.getYwryList(); // 初始化运维人员列表
  },
  methods: {
    // 初始化数据
    initData() {
      // 从路由参数或者其他方式获取数据
      const formData = this.$route.query.formData || {};
      this.myForm = { ...this.myForm, ...formData };

      // 如果是编辑模式，可以通过ID获取详细数据
      const id = this.$route.query.id;
      if (id) {
        this.loadApplicationData(id);
      }
    },

    // 加载应用数据
    async loadApplicationData(id) {
      try {
        this.loading = true;
        console.log("Loading application data for ID:", id);

        const response = await getApplication(id);
        if (response.code === 200 && response.data) {
          const data = response.data;

          // 映射API返回的数据到表单
          this.myForm = {
            ...this.myForm,
            // 基本信息
            id: data.id,
            yybm: data.yybm || "",
            name: data.name || "",
            adminId: data.adminId || "",
            yylx: data.yylx || "",
            sxsj: data.sxsj || "",
            ywjzsj: data.ywjzsj || "",
            status: data.status || 1,
            jsyj: data.jsyj || "",
            zcwj: data.zcwj || "",
            jscj: data.jscj || "",
            sftc: data.sftc || 0,
            yhfw: data.yhfw || "",
            fbd: data.fbd || "",
            wlhj: data.wlhj || "",
            sshj: data.sshj || "",
            yyly: data.yyly || "",
            isCollaboration: data.isCollaboration || 0,
            isCrossScenario: data.isCrossScenario || 0,
            isSystemConnected: data.isSystemConnected || 0,
            systemUrl: data.systemUrl || "",
            url: data.url || "",

            // 等保信息
            dbSecurityLevel: data.dbSecurityLevel || "",
            dbIsSecurityFiling: data.dbIsSecurityFiling || 0,
            dbSecurityFilingDate: data.dbSecurityFilingDate || "",
            dbSecurityFilingNo: data.dbSecurityFilingNo || "",
            dbSecurityFilingOrg: data.dbSecurityFilingOrg || "",
            dbIsSecurityAssessment: data.dbIsSecurityAssessment || 0,
            dbSecurityAssessmentOrg: data.dbSecurityAssessmentOrg || "",
            dbSecurityAssessmentScore: data.dbSecurityAssessmentScore || "",
            dbSecurityAssessmentDate: data.dbSecurityAssessmentDate || "",
            dbIsCryptoAssessment: data.dbIsCryptoAssessment || 0,
            dbCryptoAssessmentLevel: data.dbCryptoAssessmentLevel || "",
            dbCryptoAssessmentDate: data.dbCryptoAssessmentDate || "",
            dbCryptoAssessmentNo: data.dbCryptoAssessmentNo || "",

            // 单位信息
            constructionUnitId: data.constructionUnitId || "",
            businessDepartment: data.businessDepartment || "",
            unitLeader: data.unitLeader || "",
            unitType: data.unitType || "",
            phone: data.phone || "",
            address: data.address || "",

            // 厂商信息
            csDeveloperId: data.csDeveloperId || "",
            csDeveloperUserId: data.csDeveloperUserId || "",
            csOperatorId: data.csOperatorId || "",
            csOperatorContactId: data.csOperatorContactId || "",
            csSecurityVendorId: data.csSecurityVendorId || "",
            csSecurityVendorContactId: data.csSecurityVendorContactId || "",

            // 关联数据列表
            xmList: data.xmList || [],
            ipList: data.ipList || [],
            zjList: data.zjList || [],
            yzytList: data.yzytList || [],

            // 运维人员处理 - 从数组中提取第一个人员的ID
            zyywry: data.zywList && data.zywList.length > 0 ? data.zywList[0].userId : "",
            fyywry: data.fywList && data.fywList.length > 0 ? data.fywList[0].userId : "",

            // 其他字段
            remark: data.remark || ""
          };

          // 加载厂商联系人信息
          if (this.myForm.csDeveloperId) {
            this.loadDeveloperContacts(this.myForm.csDeveloperId);
          }
          if (this.myForm.csOperatorId) {
            this.loadOperatorContacts(this.myForm.csOperatorId);
          }
          if (this.myForm.csSecurityVendorId) {
            this.loadSecurityContacts(this.myForm.csSecurityVendorId);
          }

          console.log("Loaded application data:", this.myForm);
        } else {
          this.$message.error(response.msg || '获取应用详情失败');
        }
      } catch (error) {
        console.error("Error loading application data:", error);
        this.$message.error('获取应用详情失败');
      } finally {
        this.loading = false;
      }
    },

    // 保存数据
    async handleSave() {
      this.$refs.myForm.validate(async (valid) => {
        if (valid) {
          try {
            this.loading = true;
            console.log("Saving form data:", this.myForm);
            console.log("Is edit mode:", !!this.myForm.id);

            // 准备保存的数据，确保字段名与API一致
            const saveData = {
              ...this.myForm,
              // 运维人员字段映射 - 将单个ID转换为数组格式
              zywList: this.myForm.zyywry ? [{ userId: this.myForm.zyywry }] : [],
              fywList: this.myForm.fyywry ? [{ userId: this.myForm.fyywry }] : [],

              // 确保数值类型字段为数字
              adminId: this.myForm.adminId ? parseInt(this.myForm.adminId) : null,
              yylx: this.myForm.yylx ? parseInt(this.myForm.yylx) : null,
              status: this.myForm.status ? parseInt(this.myForm.status) : 1,
              jsyj: this.myForm.jsyj ? parseInt(this.myForm.jsyj) : null,
              jscj: this.myForm.jscj ? parseInt(this.myForm.jscj) : null,
              sftc: this.myForm.sftc ? parseInt(this.myForm.sftc) : 0,
              yhfw: this.myForm.yhfw ? parseInt(this.myForm.yhfw) : null,
              fbd: this.myForm.fbd ? parseInt(this.myForm.fbd) : null,
              wlhj: this.myForm.wlhj ? parseInt(this.myForm.wlhj) : null,
              sshj: this.myForm.sshj ? parseInt(this.myForm.sshj) : null,
              yyly: this.myForm.yyly ? parseInt(this.myForm.yyly) : null,
              isCollaboration: this.myForm.isCollaboration ? parseInt(this.myForm.isCollaboration) : 0,
              isCrossScenario: this.myForm.isCrossScenario ? parseInt(this.myForm.isCrossScenario) : 0,
              isSystemConnected: this.myForm.isSystemConnected ? parseInt(this.myForm.isSystemConnected) : 0,

              // 等保相关数值字段
              dbSecurityLevel: this.myForm.dbSecurityLevel ? parseInt(this.myForm.dbSecurityLevel) : null,
              dbIsSecurityFiling: this.myForm.dbIsSecurityFiling ? parseInt(this.myForm.dbIsSecurityFiling) : 0,
              dbIsSecurityAssessment: this.myForm.dbIsSecurityAssessment ? parseInt(this.myForm.dbIsSecurityAssessment) : 0,
              dbSecurityAssessmentScore: this.myForm.dbSecurityAssessmentScore ? parseFloat(this.myForm.dbSecurityAssessmentScore) : null,
              dbIsCryptoAssessment: this.myForm.dbIsCryptoAssessment ? parseInt(this.myForm.dbIsCryptoAssessment) : 0,
              dbCryptoAssessmentLevel: this.myForm.dbCryptoAssessmentLevel ? parseInt(this.myForm.dbCryptoAssessmentLevel) : null,

              // 日期字段格式化
              sxsj: this.myForm.sxsj ? this.formatDate(this.myForm.sxsj) : null,
              ywjzsj: this.myForm.ywjzsj ? this.formatDate(this.myForm.ywjzsj) : null,
              dbSecurityFilingDate: this.myForm.dbSecurityFilingDate ? this.formatDate(this.myForm.dbSecurityFilingDate) : null,
              dbSecurityAssessmentDate: this.myForm.dbSecurityAssessmentDate ? this.formatDate(this.myForm.dbSecurityAssessmentDate) : null,
              dbCryptoAssessmentDate: this.myForm.dbCryptoAssessmentDate ? this.formatDate(this.myForm.dbCryptoAssessmentDate) : null,

              // 移除表单中的单个运维人员字段和其他不需要的字段
              zyywry: undefined,
              fyywry: undefined,
              developerVendorCode: undefined,
              operatorVendorCode: undefined,
              securityVendorCode: undefined,
              developerContactPhone: undefined,
              operatorContactPhone: undefined,
              securityContactPhone: undefined,
            };

            // 如果是新增操作（没有id），则移除id字段
            if (!this.myForm.id) {
              delete saveData.id;
            }

            console.log("Prepared save data:", saveData);
            console.log("Is new record:", !this.myForm.id);

            // 统一使用编辑接口进行保存
            console.log("Calling updateApplication API");
            const response = await updateApplication(saveData);

            console.log("API response:", response);

            if (response.code === 200) {
              this.$message.success('保存成功');
              this.goBack();
            } else {
              console.error("API error:", response);
              this.$message.error(response.msg || '保存失败');
            }
          } catch (error) {
            console.error("Error saving application data:", error);
            this.$message.error('保存失败');
          } finally {
            this.loading = false;
          }
        } else {
          this.$message.error('请检查表单数据');
        }
      });
    },

    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },

    // 新增云资源
    addCloudResource() {
      this.myForm.yzytList.push({
        ip: "",
        zylx: "",
        ycpmc: "",
        sllx: "",
        gg: "",
        ktsj: "",
        jzsj: "",
      });
    },

    // 删除云资源
    removeCloudResource(index) {
      this.myForm.yzytList.splice(index, 1);
    },

    // 新增关联项目
    addRelatedProject() {
      this.myForm.xmList.push({
        xmbm: "",
        xmmc: "",
        jsbm: "",
        lxsj: "",
      });
    },

    // 删除关联项目
    removeRelatedProject(index) {
      this.myForm.xmList.splice(index, 1);
    },

    // 新增关联IP
    addRelatedIp() {
      this.myForm.ipList.push({
        ipName: "",
        ip: "",
        dkh: "",
        ipStatus: 1, // 默认为运行状态
      });
    },

    // 删除关联IP
    removeRelatedIp(index) {
      this.myForm.ipList.splice(index, 1);
    },

    // 新增使用组件
    addComponent() {
      this.myForm.zjList.push({
        type: 1, // 默认为中间件
        gylId: "",
        gys: "",
        bbh: "",
        ip: "",
      });
    },

    // 删除使用组件
    removeComponent(index) {
      this.myForm.zjList.splice(index, 1);
    },

    // 搜索组件
    async searchComponents(query) {
      if (query !== '') {
        this.componentLoading = true;
        try {
          // 这里应该调用供应链管理API来搜索组件
          // 模拟API调用
          setTimeout(() => {
            this.componentOptions = [
              { id: 1, name: 'MySQL 8.0', supplier: 'Oracle', version: '8.0.33' },
              { id: 2, name: 'Redis 6.2', supplier: 'Redis Labs', version: '6.2.7' },
              { id: 3, name: 'Nginx 1.20', supplier: 'Nginx Inc', version: '1.20.2' },
            ].filter(item => item.name.toLowerCase().includes(query.toLowerCase()));
            this.componentLoading = false;
          }, 200);
        } catch (error) {
          console.error('搜索组件失败:', error);
          this.componentOptions = [];
          this.componentLoading = false;
        }
      } else {
        this.componentOptions = [];
      }
    },

    // 搜索供应商（用于组件模块）
    async searchSupplierForComponent(query) {
      if (query !== '') {
        this.supplierLoading = true;
        try {
          const response = await listSupplier({
            pageNum: 1,
            pageSize: 50,
            gysName: query // 按供应商名称模糊查询
          });
          if (response.code === 200 && response.data && response.data.list) {
            this.supplierOptions = response.data.list.map(item => ({
              id: item.id,
              name: item.gysName,
              code: item.tyxydm // 统一社会信用代码
            }));
          } else {
            this.supplierOptions = [];
          }
        } catch (error) {
          console.error('搜索供应商失败:', error);
          this.supplierOptions = [];
        } finally {
          this.supplierLoading = false;
        }
      } else {
        this.supplierOptions = [];
      }
    },

    // 搜索开发厂商
    async searchDeveloperVendors(query) {
      if (query !== '') {
        this.developerVendorLoading = true;
        try {
          const response = await listSupplier({
            pageNum: 1,
            pageSize: 50,
            gysName: query // 按供应商名称模糊查询
          });
          if (response.code === 200 && response.data && response.data.list) {
            this.developerVendorOptions = response.data.list.map(item => ({
              id: item.id,
              name: item.gysName,
              code: item.tyxydm // 统一社会信用代码
            }));
          } else {
            this.developerVendorOptions = [];
          }
        } catch (error) {
          console.error('搜索开发厂商失败:', error);
          this.developerVendorOptions = [];
        } finally {
          this.developerVendorLoading = false;
        }
      } else {
        this.developerVendorOptions = [];
      }
    },

    // 搜索运维厂商
    async searchOperatorVendors(query) {
      if (query !== '') {
        this.operatorVendorLoading = true;
        try {
          const response = await listSupplier({
            pageNum: 1,
            pageSize: 50,
            gysName: query // 按供应商名称模糊查询
          });
          if (response.code === 200 && response.data && response.data.list) {
            this.operatorVendorOptions = response.data.list.map(item => ({
              id: item.id,
              name: item.gysName,
              code: item.tyxydm // 统一社会信用代码
            }));
          } else {
            this.operatorVendorOptions = [];
          }
        } catch (error) {
          console.error('搜索运维厂商失败:', error);
          this.operatorVendorOptions = [];
        } finally {
          this.operatorVendorLoading = false;
        }
      } else {
        this.operatorVendorOptions = [];
      }
    },

    // 搜索安全厂商
    async searchSecurityVendors(query) {
      if (query !== '') {
        this.securityVendorLoading = true;
        try {
          const response = await listSupplier({
            pageNum: 1,
            pageSize: 50,
            gysName: query // 按供应商名称模糊查询
          });
          if (response.code === 200 && response.data && response.data.list) {
            this.securityVendorOptions = response.data.list.map(item => ({
              id: item.id,
              name: item.gysName,
              code: item.tyxydm // 统一社会信用代码
            }));
          } else {
            this.securityVendorOptions = [];
          }
        } catch (error) {
          console.error('搜索安全厂商失败:', error);
          this.securityVendorOptions = [];
        } finally {
          this.securityVendorLoading = false;
        }
      } else {
        this.securityVendorOptions = [];
      }
    },

    // 搜索运维人员
    searchYwryList(query) {
      this.ywryLoading = true;
      this.getYwryList(query);
    },

    // 获取运维人员列表
    async getYwryList(keyword = '') {
      try {
        const params = {
          pageNum: 1,
          pageSize: 100, // 增加页面大小以获取更多数据
          status: 1, // 只查询正常状态的人员
        };

        // 如果有关键词，同时按userName和nickName搜索
        if (keyword && keyword.trim()) {
          const trimmedKeyword = keyword.trim();
          // 先尝试按userName搜索
          params.userName = trimmedKeyword;
        }

        console.log('搜索运维人员参数:', params);
        const response = await listMaintenancePersonnel(params);
        console.log('运维人员接口响应:', response);

        if (response.code === 200 && response.data && response.data.list) {
          let list = response.data.list;

          // 如果按userName搜索没有结果，再尝试按nickName搜索
          if (keyword && keyword.trim() && list.length === 0) {
            const nickNameParams = {
              pageNum: 1,
              pageSize: 100,
              status: 1,
              nickName: keyword.trim()
            };
            console.log('按nickName搜索参数:', nickNameParams);
            const nickNameResponse = await listMaintenancePersonnel(nickNameParams);
            console.log('按nickName搜索响应:', nickNameResponse);
            if (nickNameResponse.code === 0 && nickNameResponse.data && nickNameResponse.data.list) {
              list = nickNameResponse.data.list;
            }
          }

          this.ywryOptions = list;
          console.log('最终运维人员列表:', this.ywryOptions);
        } else {
          this.ywryOptions = [];
          console.error('获取运维人员列表失败:', response.msg || response.message);
        }
      } catch (error) {
        console.error('获取运维人员列表异常:', error);
        this.ywryOptions = [];
        this.$message.error('获取运维人员列表失败');
      } finally {
        this.ywryLoading = false;
      }
    },

    // 加载开发厂商联系人
    async loadDeveloperContacts(supplierId) {
      try {
        console.log('Loading developer contacts for supplier:', supplierId);
        const response = await listContactsBySupplier(supplierId);
        if (response.code === 200 && response.data) {
          // listNotPage接口直接返回数组数据
          const contactList = Array.isArray(response.data) ? response.data : [];
          this.developerContactOptions = contactList.map(contact => ({
            id: contact.id,
            name: contact.userName || contact.nickName || '未知联系人',
            phone: contact.phone || ''
          }));
          console.log('Developer contacts loaded:', this.developerContactOptions);
        } else {
          this.developerContactOptions = [];
          console.warn('Failed to load developer contacts:', response.msg);
        }
      } catch (error) {
        console.error('Error loading developer contacts:', error);
        this.developerContactOptions = [];
      }
    },

    // 加载运维厂商联系人
    async loadOperatorContacts(supplierId) {
      try {
        console.log('Loading operator contacts for supplier:', supplierId);
        const response = await listContactsBySupplier(supplierId);
        if (response.code === 200 && response.data) {
          // listNotPage接口直接返回数组数据
          const contactList = Array.isArray(response.data) ? response.data : [];
          this.operatorContactOptions = contactList.map(contact => ({
            id: contact.id,
            name: contact.userName || contact.nickName || '未知联系人',
            phone: contact.phone || ''
          }));
          console.log('Operator contacts loaded:', this.operatorContactOptions);
        } else {
          this.operatorContactOptions = [];
          console.warn('Failed to load operator contacts:', response.msg);
        }
      } catch (error) {
        console.error('Error loading operator contacts:', error);
        this.operatorContactOptions = [];
      }
    },

    // 加载安全厂商联系人
    async loadSecurityContacts(supplierId) {
      try {
        console.log('Loading security contacts for supplier:', supplierId);
        const response = await listContactsBySupplier(supplierId);
        if (response.code === 200 && response.data) {
          // listNotPage接口直接返回数组数据
          const contactList = Array.isArray(response.data) ? response.data : [];
          this.securityContactOptions = contactList.map(contact => ({
            id: contact.id,
            name: contact.userName || contact.nickName || '未知联系人',
            phone: contact.phone || ''
          }));
          console.log('Security contacts loaded:', this.securityContactOptions);
        } else {
          this.securityContactOptions = [];
          console.warn('Failed to load security contacts:', response.msg);
        }
      } catch (error) {
        console.error('Error loading security contacts:', error);
        this.securityContactOptions = [];
      }
    },

    // 格式化日期为 YYYY-MM-DD 格式
    formatDate(date) {
      if (!date) return null;
      if (typeof date === 'string') return date;
      if (date instanceof Date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
      }
      return null;
    }
  }
};
</script>

<style lang="scss" scoped>
.container {
  padding-bottom: 120px; // 增加底部内边距，为固定的操作栏留出空间
  background-color: #f5f5f5;
  min-height: 100vh;
  overflow-y: auto; // 确保可以滚动
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background-color: #fff;
  border-radius: 15px;
  margin-bottom: 12px;

  .back-btn {
    font-size: 16px;
    color: #0057fe;
    margin-right: 12px;

    &:hover {
      color: #003db8;
    }
  }

  .page-title {
    margin: 0;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 700;
    font-size: 20px;
    color: #1d2129;
    line-height: 28px;
  }

  .header-actions {
    display: flex;
    gap: 12px;
  }
}

.edit-form {
  .card {
    border-radius: 15px;
    padding: 20px 20px;
    box-sizing: border-box;
    background-color: #fff;
    height: auto;
    margin-bottom: 12px;
    overflow: visible; // 确保卡片内容可见

    .cardTitle {
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 700;
      font-size: 18px;
      color: #1d2129;
      line-height: 24px;
      text-align: left;
      margin-bottom: 10px;
      padding-left: 18px;
      box-sizing: border-box;
      position: relative;

      &::before {
        content: "";
        position: absolute;
        top: 2px;
        left: 0;
        width: 10px;
        height: 20px;
        background: url("~@/assets/images/cardTitle_icon.png");
        background-size: 100% 100%;
      }
    }

    .lineTitle {
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 500;
      font-size: 16px;
      color: #1d2129;
      line-height: 24px;
      text-align: left;
      margin: 12px 0 0 32px;
    }
  }
}

.itemList {
  flex-wrap: wrap;
  padding: 0 20px;
  box-sizing: border-box;

  .item_grid_3 {
    width: 30%;
    margin-left: 16px;
    margin-bottom: 20px;
  }
}

.footer-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  border-top: 1px solid #e5e6eb;
  padding: 20px;
  display: flex;
  justify-content: center;
  gap: 12px;
  z-index: 1000;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1); // 添加阴影效果
}

.tableEmpty {
  padding: 10px 0 16px 0;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  color: #1d2129;
  line-height: 22px;
  text-align: center;

  .img {
    width: 82px;
    height: 82px;
    margin-bottom: 6px;
  }
}

// 全局样式覆盖
::v-deep .el-form-item__label {
  padding: 0;
  line-height: 30px;
}

::v-deep .el-form-item {
  margin-bottom: 10px;
}

::v-deep .el-table {
  border-radius: 8px;
  overflow: hidden;
}

::v-deep .el-table th {
  background-color: #f8f9fa;
}

// 确保页面可以正常滚动
body {
  overflow-y: auto !important;
}

// 表格容器样式
::v-deep .el-table__body-wrapper {
  overflow-x: auto;
}

// 确保表格内的输入框和选择器正常显示
::v-deep .el-table .el-input,
::v-deep .el-table .el-select {
  width: 100%;
}

// 空状态样式
.empty {
  padding: 20px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #999;

  .img {
    width: 60px;
    height: 60px;
    margin-bottom: 10px;
  }
}
</style>
