import request from "@/utils/request";

// 查询工单列表
export function listGd(query) {
  return request({
    url: "/tyywpt/tTyywGd/list",
    method: "get",
    params: query,
  });
}

// 获取所有应用列表
export function listAllYy() {
  return request({
    url: "/tyywpt/tTyywYy/listAll",
    method: "get",
  });
}

// 获取工单详细信息
export function getGdInfo(query) {
  return request({
    url: "/tyywpt/tTyywGd/getInfo",
    method: "get",
    params: query,
  });
}

// 新增工单
export function addGd(data) {
  return request({
    url: "/tyywpt/tTyywGd/add",
    method: "post",
    data: data,
  });
}

// 工单处理
export function handleGd(data) {
  return request({
    url: "/tyywpt/tTyywGd/handle",
    method: "post",
    data: data,
  });
}

// 处理人员
export function listClry() {
  return request({
    url: "/tyywpt/tTyywYyry/clry",
    method: "get",
  });
}

// 查询工单处理记录列表
export function listGdLog(query) {
  return request({
    url: "/tyywpt/tTyywGdLog/list",
    method: "get",
    params: query,
  });
}

// 查询部门下拉树结构
export function deptTreeSelect() {
  return request({
    url: '/system/user/deptTreeNew',
    method: 'get'
  })
}