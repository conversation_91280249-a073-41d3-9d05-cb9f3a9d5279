<template>
  <div>
    <el-table :data="tableData" style="width: 100%" max-height="200">
      <!-- <el-table-column type="index"> </el-table-column> -->
      <el-table-column prop="id" label="序号" align="center"> </el-table-column>
      <el-table-column
        prop="yymc"
        label="应用名称"
        align="center"
        :show-overflow-tooltip="true"
      >
      </el-table-column>
      <el-table-column prop="yyzysl" label="应用资源用量"> </el-table-column>
      <el-table-column prop="ywdqsj" label="运维到期时间"> </el-table-column>
      <el-table-column prop="jscs" label="技术厂商" align="center">
      </el-table-column>
      <el-table-column prop="ywygfy" label="运维预估费用" align="center">
      </el-table-column>
      <el-table-column prop="ssbm" label="所属部门" align="center">
      </el-table-column>
      <el-table-column prop="xtfzr" label="系统负责人"> </el-table-column>
      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <el-button @click="jumpUrl(scope.row)" type="text">
            查看
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  props: {
    datalist: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      tableData: [
        {
          id: "01",
          yymc: "应用名称",
          yyzysl: "RDS 12个",
          ywdqsj: "2026年3月16日",
          jscs: "运维单位",
          ywygfy: "运维预估费用",
          ssbm: "所属部门",
          xtfzr: "系统负责人",
          url: "",
        },
        {
          id: "01",
          yymc: "应用名称",
          yyzysl: "ECS 69个",
          ywdqsj: "2026年3月16日",
          jscs: "运维单位",
          ywygfy: "运维预估费用",
          ssbm: "所属部门",
          xtfzr: "系统负责人",
          url: "",
        },
        {
          id: "01",
          yymc: "应用名称",
          yyzysl: "ECS 69个",
          ywdqsj: "2026年3月16日",
          jscs: "运维单位",
          ywygfy: "运维预估费用",
          ssbm: "所属部门",
          xtfzr: "系统负责人",
          url: "",
        },
        {
          id: "01",
          yymc: "应用名称",
          yyzysl: "ECS 69个",
          ywdqsj: "2026年3月16日",
          jscs: "运维单位",
          ywygfy: "运维预估费用",
          ssbm: "所属部门",
          xtfzr: "系统负责人",
          url: "",
        },
      ],
    };
  },
  methods: {
    jumpUrl(row) {},
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-table .el-table__cell {
  padding: 8px 0 !important;
}
</style>
