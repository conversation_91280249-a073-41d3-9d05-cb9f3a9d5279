import request from '@/utils/request'

// 查询运维人员列表
export function listMaintenancePersonnel(query) {
  return request({
    url: '/tyywpt/tTyywYyry/list',
    method: 'get',
    params: query
  })
}

// 查询运维人员详细
export function getMaintenancePersonnel(id) {
  return request({
    url: '/tyywpt/tTyywYyry/getInfo',
    method: 'get',
    params: { id: id }
  })
}

// 新增或修改运维人员
export function saveMaintenancePersonnel(data) {
  return request({
    url: '/tyywpt/tTyywYyry/addAndUpdate',
    method: 'post',
    data: data
  })
}

// 删除运维人员
export function delMaintenancePersonnel(ids) {
  return request({
    url: '/tyywpt/tTyywYyry/remove',
    method: 'delete',
    params: { ids: ids }
  })
}

// 导出运维人员列表
export function exportMaintenancePersonnel(query) {
  return request({
    url: '/tyywpt/tTyywYyry/export',
    method: 'post',
    params: query
  })
}

// 查询处置人员列表
export function listProcessingPersonnel(query) {
  return request({
    url: '/tyywpt/tTyywYyry/clry',
    method: 'get',
    params: query
  })
}

// 获取供应商选项（用于下拉选择）
export function getSuppliers(query = {}) {
  return request({
    url: '/tyywpt/tTyywGys/list',
    method: 'get',
    params: {
      pageSize: 1000, // 获取所有供应商用于下拉选择
      ...query
    }
  })
}

// 搜索供应商（支持模糊查询）
export function searchSuppliers(gysName) {
  return request({
    url: '/tyywpt/tTyywGys/list',
    method: 'get',
    params: {
      pageSize: 50,
      gysName: gysName // 按供应商名称模糊查询
    }
  })
}
